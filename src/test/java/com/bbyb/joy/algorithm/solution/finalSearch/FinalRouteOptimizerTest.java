package com.bbyb.joy.algorithm.solution.finalSearch;

import com.bbyb.joy.algorithm.bean.ParameterBean;
import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * FinalRouteOptimizer 测试类
 * 用于验证线路优化器是否能正确优化路径
 */
public class FinalRouteOptimizerTest {

    private static final Logger log = LoggerFactory.getLogger(FinalRouteOptimizerTest.class);

    @BeforeEach
    public void setUp() {
        // 设置测试参数
        ParameterBean parameter = ParameterBean.builder()
                .enableReturn(1)
                .build();
        TaskContext.setParameter(parameter);

        // 设置测试距离矩阵
        setupTestMatrix();
    }

    /**
     * 设置测试距离矩阵
     * 模拟一个简单的距离矩阵，站点按顺序排列时距离最短
     */
    private void setupTestMatrix() {
        Map<String, Map<String, Double>> matrixMap = new HashMap<>();
        
        // 站点列表：depot, 5, 7, 8, 12, 9, 10, 11
        String[] sites = {"depot", "5", "7", "8", "12", "9", "10", "11"};
        
        // 初始化矩阵
        for (String from : sites) {
            matrixMap.put(from, new HashMap<>());
            for (String to : sites) {
                if (from.equals(to)) {
                    matrixMap.get(from).put(to, 0.0);
                } else {
                    // 简单的距离计算：相邻站点距离为1，其他根据位置差计算
                    double distance = calculateTestDistance(from, to);
                    matrixMap.get(from).put(to, distance);
                }
            }
        }
        
        MatrixContext.setMatrixMap(matrixMap);
        
        // 打印距离矩阵用于调试
        log.info("测试距离矩阵:");
        for (String from : sites) {
            StringBuilder sb = new StringBuilder(from + ": ");
            for (String to : sites) {
                sb.append(String.format("%s=%.1f ", to, matrixMap.get(from).get(to)));
            }
            log.info(sb.toString());
        }
    }

    /**
     * 计算测试距离
     * 模拟地理位置：depot在中心，站点按5→7→8→12→9→10→11的顺序排列
     */
    private double calculateTestDistance(String from, String to) {
        Map<String, Integer> positions = new HashMap<>();
        positions.put("depot", 0);
        positions.put("5", 1);
        positions.put("7", 2);
        positions.put("8", 3);
        positions.put("12", 4);
        positions.put("9", 5);
        positions.put("10", 6);
        positions.put("11", 7);
        
        int fromPos = positions.get(from);
        int toPos = positions.get(to);
        
        // depot到任何站点的距离为2
        if ("depot".equals(from) || "depot".equals(to)) {
            return 2.0;
        }
        
        // 站点间距离为位置差的绝对值
        return Math.abs(fromPos - toPos) * 1.0;
    }

    @Test
    public void testRouteOptimization() {
        log.info("=== 开始路径优化测试 ===");
        
        // 创建测试站点
        SiteBean depot = new SiteBean("depot", 0.0, 0.0);
        SiteBean site5 = new SiteBean("5", 1.0, 1.0);
        SiteBean site7 = new SiteBean("7", 2.0, 2.0);
        SiteBean site8 = new SiteBean("8", 3.0, 3.0);
        SiteBean site12 = new SiteBean("12", 4.0, 4.0);
        SiteBean site9 = new SiteBean("9", 5.0, 5.0);
        SiteBean site10 = new SiteBean("10", 6.0, 6.0);
        SiteBean site11 = new SiteBean("11", 7.0, 7.0);

        // 创建一个非最优的路径顺序（故意打乱）
        List<SiteBean> sites = Arrays.asList(site11, site5, site9, site7, site12, site8, site10);
        
        SiteLineBean route = new SiteLineBean();
        route.setCode("test-route");
        route.setBeginSite(depot);
        route.setSites(new ArrayList<>(sites));
        
        // 计算原始距离
        double originalDistance = calculateRouteDistance(depot, sites);
        log.info("原始路径顺序: {}", getSiteSequence(sites));
        log.info("原始路径距离: {:.3f} km", originalDistance);
        
        // 执行优化
        List<SiteLineBean> routes = Arrays.asList(route);
        List<SiteLineBean> optimizedRoutes = FinalRouteOptimizer.optimizeAllRoutes(routes);
        
        // 检查结果
        SiteLineBean optimizedRoute = optimizedRoutes.get(0);
        double optimizedDistance = calculateRouteDistance(depot, optimizedRoute.getSites());
        
        log.info("优化后路径顺序: {}", getSiteSequence(optimizedRoute.getSites()));
        log.info("优化后路径距离: {:.3f} km", optimizedDistance);
        log.info("距离改进: {:.3f} km ({:.1f}%)", 
            originalDistance - optimizedDistance, 
            (originalDistance - optimizedDistance) / originalDistance * 100);
        
        // 验证优化效果
        if (optimizedDistance < originalDistance) {
            log.info("✅ 优化成功！距离得到改进");
        } else {
            log.warn("⚠️ 优化未改进距离，可能存在问题");
        }
        
        log.info("=== 路径优化测试完成 ===");
    }

    /**
     * 计算路径距离
     */
    private double calculateRouteDistance(SiteBean depot, List<SiteBean> sites) {
        if (sites.isEmpty()) {
            return 0.0;
        }

        double totalDistance = 0.0;
        
        // 仓库到第一个站点
        totalDistance += getDistance(depot, sites.get(0));
        
        // 站点间距离
        for (int i = 0; i < sites.size() - 1; i++) {
            totalDistance += getDistance(sites.get(i), sites.get(i + 1));
        }
        
        // 最后一个站点回仓库
        totalDistance += getDistance(sites.get(sites.size() - 1), depot);
        
        return totalDistance;
    }

    /**
     * 获取两点间距离
     */
    private double getDistance(SiteBean from, SiteBean to) {
        return MatrixContext.getMatrixMap(from.getCode()).getOrDefault(to.getCode(), 0.0);
    }

    /**
     * 获取站点序列字符串
     */
    private String getSiteSequence(List<SiteBean> sites) {
        return sites.stream()
                .map(site -> site.getCode() != null ? site.getCode() : "Site")
                .collect(java.util.stream.Collectors.joining("→"));
    }
}
