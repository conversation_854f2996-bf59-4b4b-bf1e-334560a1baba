package com.bbyb.joy.algorithm.solution.TabuSearch;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class TabuSearchIntraRouteTSPMove implements ITabuSearchMove {

    private final SiteLineBean line;
    private final List<SiteBean> optimizedOrder;
    private final double costChange;

    public TabuSearchIntraRouteTSPMove(SiteLineBean line, List<SiteBean> optimizedOrder, double costChange) {
        this.line = line;
        this.optimizedOrder = optimizedOrder;
        this.costChange = costChange;
    }

    @Override
    public double getCostChange() {
        return costChange;
    }

    @Override
    public void apply() {
        // 直接替换站点顺序
        line.getSites().clear();
        line.getSites().addAll(optimizedOrder);

        // 刷新线路元数据
        line.refreshMetaData();

        log.info("应用线路内部TSP优化：线路 {} 重新排序，距离改进: {}",
                line.getCode(), String.format("%.2f", -costChange));
    }

    @Override
    public String getTabuKey() {
        StringBuilder sb = new StringBuilder("intratsp_" + line.getCode() + "_");
        for (SiteBean site : optimizedOrder) {
            sb.append(site.getCode()).append("_");
        }
        return String.valueOf(sb.toString().hashCode());
    }

}
