package com.bbyb.joy.algorithm.solution.TabuSearch;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.bean.VehicleTypeBean;
import com.bbyb.joy.algorithm.util.VehicleUtil;

class TabuSearchSwapMove implements ITabuSearchMove {
    SiteLineBean line1, line2;
    SiteBean site1, site2;
    int index1, index2;
    double costChange;

    public TabuSearchSwapMove(SiteLineBean line1, SiteBean site1, int index1, SiteLineBean line2, SiteBean site2, int index2, double costChange) {
        this.line1 = line1;
        this.site1 = site1;
        this.index1 = index1;
        this.line2 = line2;
        this.site2 = site2;
        this.index2 = index2;
        this.costChange = costChange;
    }

    @Override
    public double getCostChange() {
        return costChange;
    }

    @Override
    public void apply() {
        // 先移除，再添加，避免索引问题
        line1.getSites().remove(site1);
        line2.getSites().remove(site2);

        line1.getSites().add(index1, site2);
        line2.getSites().add(index2, site1);

        // 重新为两条线路选择最优车型
        reoptimizeVehicleTypes();

        line1.refreshMetaData();
        line2.refreshMetaData();
    }

    /**
     * 重新为受影响的线路选择最优车型
     */
    private void reoptimizeVehicleTypes() {
        // 为线路1重新选择车型
        if (!line1.getSites().isEmpty()) {
            VehicleTypeBean optimalVehicle1 = VehicleUtil.optimalVehicleTypeWithCostForSites(line1.getSites());
            if (optimalVehicle1 != null) {
                line1.setVehicleType(optimalVehicle1);
            }
        }

        // 为线路2重新选择车型
        if (!line2.getSites().isEmpty()) {
            VehicleTypeBean optimalVehicle2 = VehicleUtil.optimalVehicleTypeWithCostForSites(line2.getSites());
            if (optimalVehicle2 != null) {
                line2.setVehicleType(optimalVehicle2);
            }
        }
    }

    @Override
    public String getTabuKey() {
        String code1 = site1.getCode();
        String code2 = site2.getCode();

        // 使用排序确保 (s1,s2) 和 (s2,s1) 的key一样
        return code1.compareTo(code2) < 0 ? "swap_" + code1 + "_" + code2 : "swap_" + code2 + "_" + code1;
    }
}
