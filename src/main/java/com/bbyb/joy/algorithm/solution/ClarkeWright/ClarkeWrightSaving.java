package com.bbyb.joy.algorithm.solution.ClarkeWright;

import com.bbyb.joy.algorithm.bean.SiteBean;

import java.util.Objects;

record ClarkeWrightSaving(SiteBean siteA, SiteBean siteB, double value) {
    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (ClarkeWrightSaving) obj;
        return Objects.equals(this.siteA, that.siteA) && Objects.equals(this.siteB, that.siteB) && Double.doubleToLongBits(this.value) == Double.doubleToLongBits(that.value);
    }
}
