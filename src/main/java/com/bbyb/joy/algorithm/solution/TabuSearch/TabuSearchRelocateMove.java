package com.bbyb.joy.algorithm.solution.TabuSearch;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.bean.VehicleTypeBean;
import com.bbyb.joy.algorithm.util.VehicleUtil;

/**
 * 尝试把一条线上的点位移到另外一条线上
 */
class TabuSearchRelocateMove implements ITabuSearchMove {
    /**
     * 尝试移动的点位信息
     */
    SiteBean siteToMove;
    /**
     * 原始线路
     */
    SiteLineBean fromLine;
    /**
     * 目的线路
     */
    SiteLineBean toLine;
    /**
     * 在原始线路上的 index
     */
    int fromIndex;
    /**
     * 在目的线路上的 index
     */
    int toIndex;
    /**
     * 评分变化值，正值上升、负值下降
     */
    double costChange;

    public TabuSearchRelocateMove(SiteBean siteToMove, SiteLineBean fromLine, SiteLineBean toLine, int fromIndex, int toIndex, double costChange) {
        this.siteToMove = siteToMove;
        this.fromLine = fromLine;
        this.toLine = toLine;
        this.fromIndex = fromIndex;
        this.toIndex = toIndex;
        this.costChange = costChange;
    }

    @Override
    public double getCostChange() {
        return costChange;
    }

    @Override
    public void apply() {
        toLine.getSites().add(toIndex, siteToMove);
        fromLine.getSites().remove(siteToMove);

        // 重新为两条线路选择最优车型
        reoptimizeVehicleTypes();

        fromLine.refreshMetaData();
        toLine.refreshMetaData();
    }

    /**
     * 重新为受影响的线路选择最优车型
     */
    private void reoptimizeVehicleTypes() {
        // 为源线路重新选择车型（站点减少后可能可以使用更小的车型）
        if (!fromLine.getSites().isEmpty()) {
            VehicleTypeBean optimalFromVehicle = VehicleUtil.optimalVehicleTypeWithCostForSites(fromLine.getSites());
            if (optimalFromVehicle != null) {
                fromLine.setVehicleType(optimalFromVehicle);
            }
        }

        // 为目标线路重新选择车型（站点增加后可能需要更大的车型）
        if (!toLine.getSites().isEmpty()) {
            VehicleTypeBean optimalToVehicle = VehicleUtil.optimalVehicleTypeWithCostForSites(toLine.getSites());
            if (optimalToVehicle != null) {
                toLine.setVehicleType(optimalToVehicle);
            }
        }
    }

    @Override
    public String getTabuKey() {
        return "relocate_" + siteToMove.getCode() + "_to_" + fromLine.getCode();
    }
}
