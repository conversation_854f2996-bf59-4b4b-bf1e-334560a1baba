package com.bbyb.joy.algorithm.solution.TabuSearch;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;

import java.util.Collections;
import java.util.List;

class TabuSearchTwoOptMove implements ITabuSearchMove {
    SiteLineBean line;
    int i, j;
    double costChange;

    public TabuSearchTwoOptMove(SiteLineBean line, int i, int j, double costChange) {
        this.line = line;
        this.i = i;
        this.j = j;
        this.costChange = costChange;
    }

    @Override
    public double getCostChange() {
        return costChange;
    }

    @Override
    public void apply() {
        List<SiteBean> sublist = line.getSites().subList(i, j + 1);
        Collections.reverse(sublist);
        line.refreshMetaData();
    }

    @Override
    public String getTabuKey() {
        // 禁忌被重新连接的边，防止短时间内再次断开
        SiteBean prevSiteToI = (i == 0) ? line.getBeginSite() : line.getSites().get(i - 1);
        SiteBean siteI = line.getSites().get(i);
        String code1 = prevSiteToI.getCode();
        String code2 = siteI.getCode();
        // 排序确保唯一性
        return "edge_" + (code1.compareTo(code2) < 0 ? code1 + "_" + code2 : code2 + "_" + code1);
    }
}
