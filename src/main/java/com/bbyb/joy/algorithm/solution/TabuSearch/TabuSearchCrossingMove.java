package com.bbyb.joy.algorithm.solution.TabuSearch;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 专门用于解决线路交叉的移动操作
 */
@Slf4j
@AllArgsConstructor
public class TabuSearchCrossingMove implements ITabuSearchMove {

    private final SiteLineBean line1;
    private final SiteLineBean line2;
    private final int crossingSegment1Start;
    private final int crossingSegment1End;
    private final int crossingSegment2Start;
    private final int crossingSegment2End;
    private final double costChange;
    private final CrossingMoveType moveType;

    @Override
    public void apply() {
        switch (moveType) {
            case REVERSE_SEGMENT_1:
                Collections.reverse(line1.getSites().subList(crossingSegment1Start, crossingSegment1End + 1));
                line1.refreshMetaData();
                break;

            case REVERSE_SEGMENT_2:
                Collections.reverse(line2.getSites().subList(crossingSegment2Start, crossingSegment2End + 1));
                line2.refreshMetaData();
                break;

            case SWAP_SEGMENTS:
                swapSegments();
                break;

            case REVERSE_BOTH_SEGMENTS:
                Collections.reverse(line1.getSites().subList(crossingSegment1Start, crossingSegment1End + 1));
                Collections.reverse(line2.getSites().subList(crossingSegment2Start, crossingSegment2End + 1));
                line1.refreshMetaData();
                line2.refreshMetaData();
                break;
        }

        log.debug("应用交叉修复移动：{}", moveType);
    }

    private void swapSegments() {
        List<SiteBean> sites1 = line1.getSites();
        List<SiteBean> sites2 = line2.getSites();

        // 提取要交换的片段
        List<SiteBean> segment1 = new ArrayList<>(sites1.subList(crossingSegment1Start, crossingSegment1End + 1));
        List<SiteBean> segment2 = new ArrayList<>(sites2.subList(crossingSegment2Start, crossingSegment2End + 1));

        // 清空原片段并插入新片段
        sites1.subList(crossingSegment1Start, crossingSegment1End + 1).clear();
        sites1.addAll(crossingSegment1Start, segment2);

        sites2.subList(crossingSegment2Start, crossingSegment2End + 1).clear();
        sites2.addAll(crossingSegment2Start, segment1);

        line1.refreshMetaData();
        line2.refreshMetaData();
    }

    @Override
    public double getCostChange() {
        return costChange;
    }

    @Override
    public String getTabuKey() {
        String code1 = line1.getCode();
        String code2 = line2.getCode();
        return "crossing_" + (code1.compareTo(code2) < 0 ? code1 + "_" + code2 : code2 + "_" + code1)
                + "_" + moveType;
    }

    /**
     * 交叉修复移动类型
     */
    public enum CrossingMoveType {
        REVERSE_SEGMENT_1,    // 反转线路1的交叉片段
        REVERSE_SEGMENT_2,    // 反转线路2的交叉片段
        SWAP_SEGMENTS,        // 交换两个交叉片段
        REVERSE_BOTH_SEGMENTS // 同时反转两个交叉片段
    }
}
