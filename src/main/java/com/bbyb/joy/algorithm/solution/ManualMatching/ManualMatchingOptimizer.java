package com.bbyb.joy.algorithm.solution.ManualMatching;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bbyb.joy.algorithm.bean.*;
import com.bbyb.joy.algorithm.context.TaskContext;
import com.bbyb.joy.algorithm.implementation.TabuSearchImplementation;
import com.bbyb.joy.algorithm.util.GEOUtil;
import com.bbyb.joy.algorithm.util.SiteLinesUtil;
import com.bbyb.joy.algorithm.util.VehicleUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ManualMatchingOptimizer {
    /**
     * 线路自交叉优化
     */
    public static void optimizeWithSelfSiteLineCrossed() {
        log.debug("开始执行线路自交叉优化！");
        /*
         * 使用数据副本进行操作
         */
        List<SiteLineBean> siteLines = new ArrayList<>(TaskContext.getCompleteSiteLines().stream().map(ObjectUtil::cloneByStream).toList());

        for (SiteLineBean siteLine : siteLines) {
            log.debug("开始执行 {} 线路的优化，当前线路包含 {} 个站点！", siteLine.getCode(), siteLine.getSites().size());

            if (siteLine.getSites().size() < 3) {
                log.debug("当前线路站点数量小于三个不需要进行优化！");
                continue;
            }

            log.debug("优化前站点顺序为：{}!", StrUtil.join(",", siteLine.getSites().stream().map(SiteBean::getCode).toArray()));

            // 通过禁忌算法获取优化后的站点排序索引
            int[] indexPathGroup = new TabuSearchImplementation().resolve(siteLine.getSites()).getPath();

            List<Integer> indexPathGroupList = Arrays.stream(indexPathGroup).boxed().toList();

            // 通过索引重新设置
            List<SiteBean> currentSites = new ArrayList<>();
            for (int optimizeIndex : indexPathGroupList) {
                SiteBean curr_site = siteLine.getSites().get(optimizeIndex);
                currentSites.add(curr_site);
            }

            log.debug("优化后站点顺序为：{}!", StrUtil.join(",", currentSites.stream().map(SiteBean::getCode).toArray()));
            siteLine.setSites(currentSites);

            log.debug("重新刷新线路元数据！");
            siteLine.refreshMetaData();
        }

        TaskContext.setCompleteSiteLines(siteLines);
    }

    /**
     * 依据已生成的线路进行合并操作，按中心点进行筛选匹配规则中可以合并的。
     */
    public static List<SiteLineBean> optimizeWithLikeSnakeMerge(List<SiteLineBean> inSiteLines) {
        ParameterBean parameter = TaskContext.getParameter();

        List<SiteLineBean> siteLines = inSiteLines.stream().map(ObjectUtil::cloneByStream).collect(Collectors.toList());
        log.debug("开始对已生成线路进行合并操作！");
        try {
            boolean mergedInLoop;
            do {
                mergedInLoop = false;
                for (int i = 0; i < siteLines.size(); i++) {
                    SiteLineBean tmpLine = siteLines.get(0);

                    List<SiteLineBean> pendingIterationSiteLines = siteLines.stream().sorted(Comparator.comparingDouble(SiteLineBean::getCenterToBeginSiteDistance)).filter(p -> !p.getCode().equals(tmpLine.getCode()) && p.getTotalVolume() < p.getVehicleType().getLoadableVolume() - tmpLine.getTotalVolume() && p.getTotalWeight() < p.getVehicleType().getLoadableWeight() - tmpLine.getTotalWeight()).toList();
                    if (pendingIterationSiteLines.isEmpty()) {
                        continue;
                    }

                    // 计算其他车次中心点距离【车1】中心点的距离
                    for (SiteLineBean siteLine : pendingIterationSiteLines) {
                        SiteLineBean comparisonResultLine = new SiteLineBean();
                        comparisonResultLine.refreshCode();
                        double compareDistances = GEOUtil.CalcEarthTwoPointDistance(tmpLine.getCenter().getLon(), tmpLine.getCenter().getLat(), siteLine.getCenter().getLon(), siteLine.getCenter().getLat());
                        siteLine.setCompareDistances(compareDistances);
                    }

                    // 将车次按照距离【车1】中心点的距离从近到远排序
                    List<SiteLineBean> comparisonResultLines = pendingIterationSiteLines.stream().sorted(Comparator.comparingDouble(SiteLineBean::getCompareDistances)).filter(n -> n.getCompareDistances() < parameter.getMergeMaximumLineCenterDistance()).toList();

                    if (comparisonResultLines.isEmpty()) {
                        continue;
                    }
                    log.debug("开始尝试对 {} 线路和筛选过线路尝试合并！", tmpLine.getCode());

                    for (SiteLineBean comparisonResultLine : comparisonResultLines) {
                        // 取原始比对车次加上最近的车次进行合并
                        SiteLineBean mergedSiteLine = SiteLinesUtil.getLineByMergeTwoLine(tmpLine, comparisonResultLine);
                        mergedSiteLine.refreshCode();
                        mergedSiteLine.refreshMetaData();

                        VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeForFSMVRP(mergedSiteLine);
                        if(vehicleType == null){
                            log.debug("合并失败！线路 {} 和线路 {} 合并后线路已超载，匹配车型失败，继续尝试下一次合并！", tmpLine.getCode(), comparisonResultLine.getCode());
                            continue;
                        }

                        if (!SiteLinesUtil.isLineNoOverLoad(vehicleType, mergedSiteLine)) {
                            log.debug("合并失败！线路 {} 和线路 {} 合并后线路已超载，继续尝试下一次合并！", tmpLine.getCode(), comparisonResultLine.getCode());
                            continue;
                        }

                        List<SiteLineBean> otherSiteLine = siteLines.stream().filter(n -> !n.getCode().equals(comparisonResultLine.getCode()) && !n.getCode().equals(tmpLine.getCode())).toList();
                        if (!SiteLinesUtil.isLineNoCrossWithCompleteSiteLines(mergedSiteLine, otherSiteLine)) {
                            log.debug("合并失败，线路 {} 和线路 {} 合并后线路和其他现有线路有交叉！", tmpLine.getCode(), comparisonResultLine.getCode());
                            continue;
                        }

                        siteLines.remove(comparisonResultLine);
                        siteLines.remove(tmpLine);
                        siteLines.add(mergedSiteLine);
                        log.debug("合并成功，线路 {} 和线路 {} 合并后新线路编码为 {}！", tmpLine.getCode(), comparisonResultLine.getCode(), mergedSiteLine.getCode());

                        mergedInLoop = true;
                        break;
                    }
                }
            } while (mergedInLoop);
        } catch (Exception e) {
            log.error("线路合并优化失败，错误原因：{}", e.getMessage());
        }
        log.debug("结束对已生成线路进行合并操作，优化后线路数量为：{}，站点数量为：{}", siteLines.size(), siteLines.stream().mapToInt(n -> n.getSites().size()).sum());

        return siteLines;
    }

    /**
     * 处理排线结果之上的点位距离其他线路中心点更近情况
     */
    public static void optimizeWithAnySiteCloserOtherSiteLineCenterMerge() {
        log.debug("开始对线路上点位具体其他线路更近情况进行优化！");
        /*
         * 获取原始线路的副本数据进行操作
         */
        List<SiteLineBean> siteLines = new ArrayList<>(TaskContext.getCompleteSiteLines().stream().map(ObjectUtil::cloneByStream).toList());

        try {
            for (SiteLineBean siteLine : siteLines) {
                GEOPointBean currentLineCenter = siteLine.getCenter();
                List<SiteBean> currentLineSites = new ArrayList<>(siteLine.getSites().stream().map(ObjectUtil::cloneByStream).toList());

                for (SiteBean site : currentLineSites) {
                    // 当前点位到当前线路中心点距离
                    double currentSiteToCenterDistance = GEOUtil.CalcEarthTwoPointDistance(site.getLon(), site.getLat(), currentLineCenter.getLon(), currentLineCenter.getLat());

                    List<SiteLineBean> pendingSiteLines = siteLines.stream().filter(o -> !o.getCode().equals(siteLine.getCode())).filter(n -> GEOUtil.CalcEarthTwoPointDistance(site.getLon(), site.getLat(), n.getCenter().getLon(), n.getCenter().getLat()) < currentSiteToCenterDistance).toList();
                    if (pendingSiteLines.isEmpty()) {
                        continue;
                    }

                    log.debug("{} 线路上 {} 站点有其他距离更近的线路，尝试进行点位迁移。", siteLine.getCode(), site.getCode());
                    for (SiteLineBean pendingMergedSiteLine : pendingSiteLines) {
                        pendingMergedSiteLine.getSites().add(site);

                        VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeForFSMVRP(pendingMergedSiteLine);
                        if(vehicleType == null){
                            log.debug("{} 线路上 {} 站点有其他距离更近的线路，匹配车型失败，尝试失败",siteLine.getCode(), pendingMergedSiteLine.getCode());
                            break;
                        }
                        if (SiteLinesUtil.isLineNoOverLoad(vehicleType, pendingMergedSiteLine)) {
                            siteLine.getSites().remove(site);
                            siteLine.refreshMetaData();

                            pendingMergedSiteLine.refreshMetaData();

                            log.debug("点位迁移成功，{} 线路上 {} 站点已迁移到 {} 线路上", siteLine.getCode(), site.getCode(), pendingMergedSiteLine.getCode());
                            break;
                        } else {
                            pendingMergedSiteLine.getSites().remove(site);
                            log.debug("点位迁移失败，{} 线路上 {} 站点尝试迁移到 {} 线路上超载！", siteLine.getCode(), site.getCode(), pendingMergedSiteLine.getCode());
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("点位迁移优化失败，错误原因：{}", e.getMessage());
        }

        siteLines = siteLines.stream().filter(n -> !n.getSites().isEmpty()).toList();
        TaskContext.setCompleteSiteLines(siteLines);

        log.debug("对线路上点位具体其他线路更近情况进行优化操作结束，合并后线路以及站点对应数量以下：");
        TaskContext.getCompleteSiteLines().forEach(siteLine -> {
            log.debug("线路 {} 下包含 {} 个站点信息！", siteLine.getCode(), siteLine.getSites().size());
        });
        log.debug("结束对线路上点位具体其他线路更近情况进行优化操作，优化后线路数量为：{}，站点数量为：{}", TaskContext.getCompleteSiteLines().size(), TaskContext.getCompleteSiteLines().stream().mapToInt(n -> n.getSites().size()).sum());
    }

    /**
     * 处理排线结果之上的点位距离其他线路中心点更近情况
     */
    public static void optimizeWithAnySiteCloserOtherSiteLineBeginOrEndMerge() {
        log.debug("开始对线路上点位具体其他线路更近情况进行优化！");
        /*
         * 获取原始线路的副本数据进行操作
         */
        List<SiteLineBean> siteLines = new ArrayList<>(TaskContext.getCompleteSiteLines().stream().map(ObjectUtil::cloneByStream).toList());

        try {
            for (SiteLineBean siteLine : siteLines) {
                GEOPointBean currentLineCenter = siteLine.getCenter();
                List<SiteBean> currentLineSites = new ArrayList<>(siteLine.getSites().stream().map(ObjectUtil::cloneByStream).toList());

                for (SiteBean site : currentLineSites) {
                    // 当前点位到当前线路中心点距离
                    double currentSiteToCenterDistance = GEOUtil.CalcEarthTwoPointDistance(site.getLon(), site.getLat(), currentLineCenter.getLon(), currentLineCenter.getLat());

                    List<SiteLineBean> pendingSiteLines = siteLines.stream().filter(o -> !o.getCode().equals(siteLine.getCode())).filter(n -> GEOUtil.CalcEarthTwoPointDistance(site.getLon(), site.getLat(), n.getSites().get(0).getLon(), n.getSites().get(0).getLat()) < currentSiteToCenterDistance).toList();
                    if (pendingSiteLines.isEmpty()) {
                        continue;
                    }

                    log.debug("{} 线路上 {} 站点有其他距离更近的线路，尝试进行点位迁移。", siteLine.getCode(), site.getCode());
                    for (SiteLineBean pendingMergedSiteLine : pendingSiteLines) {
                        pendingMergedSiteLine.getSites().add(site);

                        VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeForFSMVRP(pendingMergedSiteLine);
                        if(vehicleType == null){
                            log.debug("{} 线路上 {} 站点有其他距离更近的线路，匹配车型失败。", siteLine.getCode(), site.getCode());
                            break;
                        }
                        if (SiteLinesUtil.isLineNoOverLoad(vehicleType, pendingMergedSiteLine)) {
                            siteLine.getSites().remove(site);
                            siteLine.refreshMetaData();

                            pendingMergedSiteLine.refreshMetaData();

                            log.debug("点位迁移成功，{} 线路上 {} 站点已迁移到 {} 线路上", siteLine.getCode(), site.getCode(), pendingMergedSiteLine.getCode());
                            break;
                        } else {
                            pendingMergedSiteLine.getSites().remove(site);
                            log.debug("点位迁移失败，{} 线路上 {} 站点尝试迁移到 {} 线路上超载！", siteLine.getCode(), site.getCode(), pendingMergedSiteLine.getCode());
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("点位迁移优化失败，错误原因：{}", e.getMessage());
        }

        siteLines = siteLines.stream().filter(n -> !n.getSites().isEmpty()).toList();
        TaskContext.setCompleteSiteLines(siteLines);

        log.debug("对线路上点位具体其他线路更近情况进行优化操作结束，合并后线路以及站点对应数量以下：");
        TaskContext.getCompleteSiteLines().forEach(siteLine -> {
            log.debug("线路 {} 下包含 {} 个站点信息！", siteLine.getCode(), siteLine.getSites().size());
        });
        log.debug("结束对线路上点位具体其他线路更近情况进行优化操作，优化后线路数量为：{}，站点数量为：{}", TaskContext.getCompleteSiteLines().size(), TaskContext.getCompleteSiteLines().stream().mapToInt(n -> n.getSites().size()).sum());
    }
}

