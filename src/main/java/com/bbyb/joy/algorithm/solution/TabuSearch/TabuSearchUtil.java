package com.bbyb.joy.algorithm.solution.TabuSearch;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.algorithm.bean.ParameterBean;
import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import com.bbyb.joy.algorithm.util.TimeWindowUtil;
import com.bbyb.joy.algorithm.util.VehicleUtil;
import lombok.extern.slf4j.Slf4j;

import java.awt.geom.Line2D;
import java.util.*;

@Slf4j
public class TabuSearchUtil {

    /**
     * 根据总站点数计算最大迭代次数
     * @param totalSites 总站点数
     * @return 最大迭代次数
     */
    public static int calculateMaxIterations(int totalSites) {
        if (totalSites <= 20) {
            return 500;  // 小规模问题
        } else if (totalSites <= 50) {
            return 800;  // 中小规模问题
        } else if (totalSites <= 100) {
            return 1200; // 中等规模问题
        } else if (totalSites <= 200) {
            return 1800; // 中大规模问题
        } else {
            return Math.min(3000, totalSites * 15); // 大规模问题，但设置上限
        }
    }

    /**
     * 根据总站点数计算禁忌期长度
     * @param totalSites 总站点数
     * @return 禁忌期长度
     */
    public static int calculateTabuTenure(int totalSites) {
        // 禁忌期长度通常为站点数的10-20%，但设置合理的上下限
        int tenure = Math.max(5, totalSites / 8); // 最小为5
        return Math.min(50, tenure); // 最大为50
    }


    /**
     * 从矩阵中获取两个站点之间的距离信息
     *
     * @param site1 起始站点
     * @param site2 目的站点
     * @return 地球距离
     */
    public static double getDistance(SiteBean site1, SiteBean site2) {
        if (site1 == null || site2 == null) return 0.0;
        return MatrixContext.getMatrixMap(site1.getCode()).getOrDefault(site2.getCode(), Double.MAX_VALUE);
    }

    /**
     * 计算单条线路的集中度成本（使用最小生成树总长度）
     * 返回值越大，表示越分散
     *
     * @param siteLine 线路
     * @return 评估出来惩罚值
     */
    static double calculateConcentrationCostForLine(SiteLineBean siteLine) {
        List<SiteBean> sites = new ArrayList<>(siteLine.getSites());
        if (sites.size() <= 1) {
            return 0.0;
        }

        // 计算最小生成树总长度
        double mstLength = calculateMinimumSpanningTreeLength(sites);

        // 调整惩罚系数，可以根据实际情况调整
        double concentrationFactor = 10000.0;

        // 考虑站点数量的影响，站点越多，集中度要求可以适当放宽
        double sizeAdjustment = Math.log(sites.size() + 1);

        return mstLength * concentrationFactor / sizeAdjustment;
    }

    /**
     * 计算最小生成树总长度（使用Prim算法）
     */
    private static double calculateMinimumSpanningTreeLength(List<SiteBean> sites) {
        if (sites.size() <= 1) {
            return 0.0;
        }

        int n = sites.size();
        double[] key = new double[n]; // 存储每个顶点到MST的最小距离
        boolean[] inMST = new boolean[n]; // 标记顶点是否已加入MST

        // 初始化所有key为无穷大，所有顶点都未加入MST
        Arrays.fill(key, Double.MAX_VALUE);
        Arrays.fill(inMST, false);

        // 选择第一个顶点作为起点
        key[0] = 0;

        double totalLength = 0.0;

        for (int count = 0; count < n; count++) {
            // 找到未加入MST且距离最小的顶点
            int u = -1;
            double minKey = Double.MAX_VALUE;
            for (int i = 0; i < n; i++) {
                if (!inMST[i] && key[i] < minKey) {
                    minKey = key[i];
                    u = i;
                }
            }

            // 将找到的顶点加入MST
            inMST[u] = true;
            totalLength += key[u]; // 累加边的长度

            // 更新相邻顶点的key值
            for (int v = 0; v < n; v++) {
                // 计算两点间的距离
                double distance = calculateHaversineDistance(
                        sites.get(u).getLat(), sites.get(u).getLon(),
                        sites.get(v).getLat(), sites.get(v).getLon()
                );

                // 如果v未加入MST且u到v的距离小于当前key[v]
                if (!inMST[v] && distance < key[v]) {
                    key[v] = distance;
                }
            }
        }

        return totalLength;
    }

    static double calculateConcentrationCostForLineExcludeSite(SiteLineBean siteLine, SiteBean excludeSite) {
        List<SiteBean> sites = new ArrayList<>(siteLine.getSites());
        if (sites.size() <= 1) {
            return 0.0;
        }
        sites.remove(excludeSite);

        // 计算最小生成树总长度
        double mstLength = calculateMinimumSpanningTreeLength(sites);

        // 调整惩罚系数
        double concentrationFactor = 10000.0;

        // 考虑站点数量的影响
        double sizeAdjustment = Math.log(sites.size() + 1);

        return mstLength * concentrationFactor / sizeAdjustment;
    }

    static double calculateConcentrationCostForLineIncludeSite(SiteLineBean siteLine, SiteBean includeSite) {
        List<SiteBean> sites = new ArrayList<>(siteLine.getSites());

        if (sites.stream().noneMatch(site -> includeSite == site)) {
            sites.add(includeSite);
        }

        if (sites.size() <= 1) {
            return 0.0;
        }

        // 计算最小生成树总长度
        double mstLength = calculateMinimumSpanningTreeLength(sites);

        // 调整惩罚系数
        double concentrationFactor = 10000.0;

        // 考虑站点数量的影响
        double sizeAdjustment = Math.log(sites.size() + 1);

        return mstLength * concentrationFactor / sizeAdjustment;
    }

    // Haversine公式计算两点间的地理距离（单位：公里）
    private static double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // 地球半径，单位公里

        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 计算所有线路的惩罚值
     *
     * @param siteLines 目前所有线路
     * @return 合计惩罚值
     */
    static double calculateTotalCost(List<SiteLineBean> siteLines) {
        double totalDistanceCost = 0;
        double totalConcentrationCost = 0;
        double totalTimeWindowPenalty = 0;
        double totalCrossingPenalty = 0;

        for (SiteLineBean line : siteLines) {
            totalDistanceCost += line.getTotalDistance();
            totalConcentrationCost += calculateConcentrationCostForLine(line);

            // 如果时间窗不满足，增加大惩罚值
            if (!TimeWindowUtil.isRouteTimeWindowFeasible(line)) {
                totalTimeWindowPenalty += 1000000.0; // 大惩罚值
            }

            // 线路交叉惩罚
            for (SiteLineBean otherLine : siteLines) {
                if (otherLine != line) {
                    int crossingCount = countLineCrossings(line, otherLine);
                    if (crossingCount > 0) {
                        // 交叉数量越多，惩罚越重
                        totalCrossingPenalty += crossingCount * totalCrossingPenalty;
                    }
                }
            }
        }

        double totalVehicleCost = siteLines.size() * 1000;

        // 所有线路总距离加上集中度惩罚值和时间窗惩罚
        return totalDistanceCost + totalVehicleCost + totalConcentrationCost + totalTimeWindowPenalty + totalCrossingPenalty;
    }


    /**
     * 计算两条线路的交叉点数量
     */
    public static int countLineCrossings(SiteLineBean line1, SiteLineBean line2) {
        List<SiteBean> sites1 = line1.getSites();
        List<SiteBean> sites2 = line2.getSites();

        if (sites1.size() < 2 || sites2.size() < 2) {
            return 0;
        }

        int crossingCount = 0;

        for (int i = 0; i < sites1.size() - 1; i++) {
            for (int j = 0; j < sites2.size() - 1; j++) {
                SiteBean p1 = sites1.get(i);
                SiteBean p2 = sites1.get(i + 1);
                SiteBean p3 = sites2.get(j);
                SiteBean p4 = sites2.get(j + 1);

                if (Line2D.linesIntersect(p1.getLon(), p1.getLat(), p2.getLon(), p2.getLat(),
                        p3.getLon(), p3.getLat(), p4.getLon(), p4.getLat())) {
                    crossingCount++;
                }
            }
        }

        return crossingCount;
    }

    /**
     * 计算移动操作对交叉的影响
     */
    static double calculateCrossingChangeForRelocate(SiteBean siteToMove, SiteLineBean fromLine,
                                                     int fromIndex, SiteLineBean toLine, int toIndex,
                                                     List<SiteLineBean> allLines) {
        // 计算移动前的交叉数量
        int originalCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != fromLine && otherLine != toLine) {
                originalCrossings += countLineCrossings(fromLine, otherLine);
                originalCrossings += countLineCrossings(toLine, otherLine);
            }
        }
        if (fromLine != toLine) {
            originalCrossings += countLineCrossings(fromLine, toLine);
        }

        // 模拟移动操作
        List<SiteBean> tempFromSites = new ArrayList<>(fromLine.getSites());
        List<SiteBean> tempToSites = new ArrayList<>(toLine.getSites());

        tempFromSites.remove(fromIndex);
        tempToSites.add(toIndex, siteToMove);

        // 创建临时线路对象
        SiteLineBean tempFromLine = createTempLine(fromLine, tempFromSites);
        SiteLineBean tempToLine = createTempLine(toLine, tempToSites);

        // 计算移动后的交叉数量
        int newCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != fromLine && otherLine != toLine) {
                newCrossings += countLineCrossings(tempFromLine, otherLine);
                newCrossings += countLineCrossings(tempToLine, otherLine);
            }
        }
        if (fromLine != toLine) {
            newCrossings += countLineCrossings(tempFromLine, tempToLine);
        }

        return (newCrossings - originalCrossings) * 50000.0; // 交叉变化的惩罚
    }

    /**
     * 计算移动后交叉数量是否增加，增加不允许交换
     */
    static boolean calculateCrossingChangeForRelocateFlag(SiteBean siteToMove, SiteLineBean fromLine,
                                                     int fromIndex, SiteLineBean toLine, int toIndex) {

        List<SiteLineBean> allLines = TaskContext.getCompleteSiteLines();

        // 计算移动前的交叉数量
        int originalCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != fromLine && otherLine != toLine) {
                originalCrossings += countLineCrossings(fromLine, otherLine);
                originalCrossings += countLineCrossings(toLine, otherLine);
            }
        }
        if (fromLine != toLine) {
            originalCrossings += countLineCrossings(fromLine, toLine);
        }

        // 模拟移动操作
        List<SiteBean> tempFromSites = new ArrayList<>(fromLine.getSites());
        List<SiteBean> tempToSites = new ArrayList<>(toLine.getSites());

        tempFromSites.remove(fromIndex);
        tempToSites.add(toIndex, siteToMove);

        // 创建临时线路对象
        SiteLineBean tempFromLine = createTempLine(fromLine, tempFromSites);
        SiteLineBean tempToLine = createTempLine(toLine, tempToSites);

        // 计算移动后的交叉数量
        int newCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != fromLine && otherLine != toLine) {
                newCrossings += countLineCrossings(tempFromLine, otherLine);
                newCrossings += countLineCrossings(tempToLine, otherLine);
            }
        }
        if (fromLine != toLine) {
            newCrossings += countLineCrossings(tempFromLine, tempToLine);
        }

        return newCrossings - originalCrossings > 0;
    }


    /**
     * 计算交换操作对交叉的影响
     */
    static double calculateCrossingChangeForSwap(SiteLineBean line1, int index1, SiteBean site1,
                                                 SiteLineBean line2, int index2, SiteBean site2,
                                                 List<SiteLineBean> allLines) {
        // 计算交换前的交叉数量
        int originalCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != line1 && otherLine != line2) {
                originalCrossings += countLineCrossings(line1, otherLine);
                originalCrossings += countLineCrossings(line2, otherLine);
            }
        }
        originalCrossings += countLineCrossings(line1, line2);

        // 模拟交换操作
        List<SiteBean> tempSites1 = new ArrayList<>(line1.getSites());
        List<SiteBean> tempSites2 = new ArrayList<>(line2.getSites());

        tempSites1.set(index1, site2);
        tempSites2.set(index2, site1);

        SiteLineBean tempLine1 = createTempLine(line1, tempSites1);
        SiteLineBean tempLine2 = createTempLine(line2, tempSites2);

        // 计算交换后的交叉数量
        int newCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != line1 && otherLine != line2) {
                newCrossings += countLineCrossings(tempLine1, otherLine);
                newCrossings += countLineCrossings(tempLine2, otherLine);
            }
        }
        newCrossings += countLineCrossings(tempLine1, tempLine2);

        return (newCrossings - originalCrossings) * 50000.0;
    }

    /**
     * 创建临时线路对象用于交叉计算
     */
    protected static SiteLineBean createTempLine(SiteLineBean originalLine, List<SiteBean> sites) {
        SiteLineBean tempLine = BeanUtil.toBean(originalLine, SiteLineBean.class);
        tempLine.setBeginSite(originalLine.getBeginSite());
        tempLine.setSites(sites);
        tempLine.setVehicleType(originalLine.getVehicleType());
        return tempLine;
    }

    /**
     * 迁移点位到另一条线路上的惩罚值
     *
     * @param siteToMove 迁移的点位
     * @param fromLine   原始线路
     * @param fromIndex  点位在原始线路的索引
     * @param toLine     目的线路
     * @param toIndex    点位在目的线路的索引
     * @return 当前调整之后的惩罚值
     */
    static double calculateRelocateCostChange(SiteBean siteToMove, SiteLineBean fromLine, int fromIndex, SiteLineBean toLine, int toIndex) {
        ParameterBean parameter = TaskContext.getParameter();
        List<SiteBean> fromSites = fromLine.getSites();
        List<SiteBean> toSites = toLine.getSites();

        SiteBean prevInFrom = (fromIndex == 0) ? fromLine.getBeginSite() : fromSites.get(fromIndex - 1);
        SiteBean nextInFrom = (fromIndex == fromSites.size() - 1) ? fromLine.getBeginSite() : fromSites.get(fromIndex + 1);
        double costRemoved = getDistance(prevInFrom, siteToMove) + getDistance(siteToMove, nextInFrom) - getDistance(prevInFrom, nextInFrom);

        SiteBean prevInTo = (toIndex == 0) ? toLine.getBeginSite() : toSites.get(toIndex - 1);
        SiteBean nextInTo = (toIndex == toSites.size()) ? toLine.getBeginSite() : toSites.get(toIndex);
        double costAdded = getDistance(prevInTo, siteToMove) + getDistance(siteToMove, nextInTo) - getDistance(prevInTo, nextInTo);

        // 检查是否超过最大距离限制
        double distPrevToSite = getDistance(prevInTo, siteToMove);
        double distSiteToNext = getDistance(siteToMove, nextInTo);
        // 计算惩罚值
        double penalty = 0;
        if (distPrevToSite > parameter.getBetweenMaximumDistance()) {
            penalty += (distPrevToSite - parameter.getBetweenMaximumDistance());
        }
        if (distSiteToNext > parameter.getBetweenMaximumDistance()) {
            penalty += (distSiteToNext - parameter.getBetweenMaximumDistance());
        }

        // 交叉影响计算
        double crossingChange = 0;
        List<SiteLineBean> allLines = TaskContext.getCompleteSiteLines();
        if (allLines != null && allLines.size() > 1) {
            crossingChange = calculateCrossingChangeForRelocate(siteToMove, fromLine, fromIndex,
                    toLine, toIndex, allLines);
        }

        double prevConcentrationCost = calculateConcentrationCostForLine(fromLine) + calculateConcentrationCostForLine(toLine);
        double nextConcentrationCost = calculateConcentrationCostForLineExcludeSite(fromLine, siteToMove) + calculateConcentrationCostForLineIncludeSite(toLine, siteToMove);
        return costAdded - costRemoved + nextConcentrationCost - prevConcentrationCost + penalty + crossingChange;
    }

    static double calculateSwapCostChange(SiteLineBean line1, int index1, SiteBean site1, SiteLineBean line2, int index2, SiteBean site2) {
        ParameterBean parameter = TaskContext.getParameter();
        List<SiteBean> sites1 = line1.getSites();
        SiteBean prev1 = (index1 == 0) ? null : sites1.get(index1 - 1);
        SiteBean next1 = (index1 == sites1.size() - 1) ? null : sites1.get(index1 + 1);

        double line1_removed_s1 = getDistance(prev1, next1) - (getDistance(prev1, site1) + getDistance(site1, next1));
        double line1_added_s2 = (getDistance(prev1, site2) + getDistance(site2, next1)) - getDistance(prev1, next1);
        double costChangeOnLine1 = line1_removed_s1 + line1_added_s2;

        List<SiteBean> sites2 = line2.getSites();
        SiteBean prev2 = (index2 == 0) ? null : sites2.get(index2 - 1);
        SiteBean next2 = (index2 == sites2.size() - 1) ? null : sites2.get(index2 + 1);

        double penalty = 0;
        // 计算line1中的惩罚值
        if (prev1 != null) {
            double dist = getDistance(prev1, site2);
            if (dist > parameter.getBetweenMaximumDistance()) {
                penalty += (dist - parameter.getBetweenMaximumDistance());
            }
        }
        if (next1 != null) {
            double dist = getDistance(site2, next1);
            if (dist > parameter.getBetweenMaximumDistance()) {
                penalty += (dist - parameter.getBetweenMaximumDistance());
            }
        }
        double line2_removed_s2 = getDistance(prev2, next2) - (getDistance(prev2, site2) + getDistance(site2, next2));
        double line2_added_s1 = (getDistance(prev2, site1) + getDistance(site1, next2)) - getDistance(prev2, next2);
        double costChangeOnLine2 = line2_removed_s2 + line2_added_s1;

        // 新增：交叉影响计算
        double crossingChange = 0;
        List<SiteLineBean> allLines = TaskContext.getCompleteSiteLines();
        if (allLines != null && allLines.size() > 1) {
            crossingChange = calculateCrossingChangeForSwap(line1, index1, site1,
                    line2, index2, site2, allLines);
        }

        return costChangeOnLine1 + costChangeOnLine2 + penalty + crossingChange;
    }

    public static double calculateTwoOptCostChange(SiteLineBean line, int i, int j) {
        List<SiteBean> sites = line.getSites();
        SiteBean depot = line.getBeginSite();
        int n = sites.size();

        if (i >= j || i < 0 || j >= n) return Double.MAX_VALUE;

        // 计算当前路径的相关边距离
        double oldCost = 0;
        double newCost = 0;

        // 当前路径涉及的边
        if (i == 0) {
            // 如果i=0，涉及从仓库到第一个站点的边
            oldCost += getDistance(depot, sites.get(0));
            oldCost += getDistance(sites.get(i), sites.get(i + 1));
        } else {
            oldCost += getDistance(sites.get(i - 1), sites.get(i));
            if (i + 1 < n) {
                oldCost += getDistance(sites.get(i), sites.get(i + 1));
            }
        }

        if (j + 1 < n) {
            oldCost += getDistance(sites.get(j), sites.get(j + 1));
        } else {
            // 如果j是最后一个站点，考虑返回仓库的距离
            ParameterBean parameter = TaskContext.getParameter();
            if (parameter != null && parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1) {
                oldCost += getDistance(sites.get(j), depot);
            }
        }

        // 2-Opt操作后的新路径
        List<SiteBean> newSites = new ArrayList<>(sites);
        // 反转i到j之间的子序列
        Collections.reverse(newSites.subList(i, j + 1));

        // 计算新路径的相关边距离
        if (i == 0) {
            newCost += getDistance(depot, newSites.get(0));
            newCost += getDistance(newSites.get(i), newSites.get(i + 1));
        } else {
            newCost += getDistance(newSites.get(i - 1), newSites.get(i));
            if (i + 1 < n) {
                newCost += getDistance(newSites.get(i), newSites.get(i + 1));
            }
        }

        if (j + 1 < n) {
            newCost += getDistance(newSites.get(j), newSites.get(j + 1));
        } else {
            ParameterBean parameter = TaskContext.getParameter();
            if (parameter != null && parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1) {
                newCost += getDistance(newSites.get(j), depot);
            }
        }

        return newCost - oldCost;
    }

    /**
     * 计算2-Opt操作对交叉的影响
     */
    static double calculateCrossingChangeForTwoOpt(SiteLineBean line, int i, int j,
                                                   List<SiteLineBean> allLines) {
        // 计算原始交叉数量
        int originalCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != line) {
                originalCrossings += countLineCrossings(line, otherLine);
            }
        }

        // 模拟2-Opt操作
        List<SiteBean> tempSites = new ArrayList<>(line.getSites());
        Collections.reverse(tempSites.subList(i, j + 1));

        SiteLineBean tempLine = createTempLine(line, tempSites);

        // 计算2-Opt后的交叉数量
        int newCrossings = 0;
        for (SiteLineBean otherLine : allLines) {
            if (otherLine != line) {
                newCrossings += countLineCrossings(tempLine, otherLine);
            }
        }

        return (newCrossings - originalCrossings) * 50000.0;
    }


    /**
     * 为指定站点寻找邻近线路
     */
    public static List<SiteLineBean> findNearbyLinesForSite(SiteBean siteToMove, List<SiteLineBean> allLines, SiteLineBean excludeLine) {
        if (allLines.size() <= 5) {
            return allLines.stream().filter(line -> line != excludeLine).toList();
        }

        // 计算站点到各线路的最短距离
        List<SiteLineBean> sortedLines = allLines.stream()
                .filter(line -> line != excludeLine)
                .sorted((line1, line2) -> {
                    double dist1 = getMinDistanceToLine(siteToMove, line1);
                    double dist2 = getMinDistanceToLine(siteToMove, line2);
                    return Double.compare(dist1, dist2);
                })
                .toList();

        // 只返回最近的几条线路
        int maxLines = Math.min(6, sortedLines.size());
        return sortedLines.subList(0, maxLines);
    }
    /**
     * 计算站点到线路的最短距离
     */
    private static double getMinDistanceToLine(SiteBean site, SiteLineBean line) {
        double minDistance = Double.MAX_VALUE;

        // 检查到起点的距离
        minDistance = Math.min(minDistance, TabuSearchUtil.getDistance(site, line.getBeginSite()));

        // 采样检查线路上的关键站点
        List<SiteBean> sites = line.getSites();
        int step = Math.max(1, sites.size() / 3); // 采样1/3的站点

        for (int i = 0; i < sites.size(); i += step) {
            double distance = TabuSearchUtil.getDistance(site, sites.get(i));
            minDistance = Math.min(minDistance, distance);
        }

        // 确保检查最后一个站点
        if (!sites.isEmpty()) {
            minDistance = Math.min(minDistance, TabuSearchUtil.getDistance(site, sites.get(sites.size() - 1)));
        }

        return minDistance;
    }



    /**
     * 为单条线路求解TSP问题
     */
    public static List<SiteBean> solveTSPForRoute(List<SiteBean> sites, SiteBean depot) {
        if (sites.size() <= 2) {
            return new ArrayList<>(sites);
        }

        // 1. 最近邻算法生成初始解
        List<SiteBean> bestTour = nearestNeighborTSP(sites, depot);
        double bestDistance = calculateRouteDistance(bestTour, depot);

        // 2. 2-opt改进
        boolean improved = true;
        int maxIterations = 100;
        int iteration = 0;

        while (improved && iteration < maxIterations) {
            improved = false;
            iteration++;

            for (int i = 1; i < bestTour.size() - 1; i++) {
                for (int j = i + 2; j < bestTour.size(); j++) {
                    // 尝试2-opt交换
                    List<SiteBean> newTour = new ArrayList<>(bestTour);
                    Collections.reverse(newTour.subList(i, j + 1));

                    double newDistance = calculateRouteDistance(newTour, depot);
                    if (newDistance < bestDistance) {
                        bestTour = newTour;
                        bestDistance = newDistance;
                        improved = true;
                    }
                }
            }
        }

        return bestTour;
    }

    /**
     * 最近邻TSP算法
     */
    private static List<SiteBean> nearestNeighborTSP(List<SiteBean> sites, SiteBean depot) {
        List<SiteBean> unvisited = new ArrayList<>(sites);
        List<SiteBean> tour = new ArrayList<>();

        // 找到距离仓库最近的站点作为起点
        SiteBean current = findNearestSite(depot, unvisited);
        unvisited.remove(current);
        tour.add(current);

        // 贪心选择最近的未访问站点
        while (!unvisited.isEmpty()) {
            SiteBean nearest = findNearestSite(current, unvisited);
            unvisited.remove(nearest);
            tour.add(nearest);
            current = nearest;
        }

        return tour;
    }

    /**
     * 找到距离当前站点最近的站点
     */
    private static SiteBean findNearestSite(SiteBean current, List<SiteBean> candidates) {
        SiteBean nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (SiteBean candidate : candidates) {
            double distance = TabuSearchUtil.getDistance(current, candidate);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = candidate;
            }
        }

        return nearest;
    }

    /**
     * 计算线路总距离
     * 根据enableReturn配置决定是否包含返仓距离
     */
    public static double calculateRouteDistance(List<SiteBean> sites, SiteBean depot) {
        if (sites.isEmpty()) return 0.0;

        double totalDistance = 0.0;

        // 从仓库到第一个站点
        totalDistance += TabuSearchUtil.getDistance(depot, sites.get(0));

        // 站点间距离
        for (int i = 0; i < sites.size() - 1; i++) {
            totalDistance += TabuSearchUtil.getDistance(sites.get(i), sites.get(i + 1));
        }

        // 根据enableReturn配置决定是否包含返仓距离
        ParameterBean parameter = TaskContext.getParameter();
        if (parameter != null && parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1) {
            totalDistance += TabuSearchUtil.getDistance(sites.get(sites.size() - 1), depot);
        }

        return totalDistance;
    }

    /**
     * 生成站点顺序的哈希值（用于禁忌）
     */
    public static String generateOrderHash(List<SiteBean> sites) {
        StringBuilder sb = new StringBuilder();
        for (SiteBean site : sites) {
            sb.append(site.getCode()).append("_");
        }
        return String.valueOf(sb.toString().hashCode());
    }


    /**
     * 检查2-Opt操作后是否满足站点间最大间距约束
     */
    public static boolean isTwoOptDistanceConstraintValid(SiteLineBean line, int i, int j) {

        ParameterBean parameter = TaskContext.getParameter();

        List<SiteBean> sites = line.getSites();
        int n = sites.size();

        // 模拟2-Opt操作
        List<SiteBean> tempSites = new ArrayList<>(sites);
        Collections.reverse(tempSites.subList(i, j + 1));

        // 检查操作后相邻站点间的距离
        for (int k = 0; k < n - 1; k++) {
            SiteBean currentSite = tempSites.get(k);
            SiteBean nextSite = tempSites.get(k + 1);

            double distance = getDistance(currentSite, nextSite);

            if (distance > parameter.getBetweenMaximumDistance()) {
                return false;
            }
        }

        return true;
    }


    /**
     * 检查路径是否满足站点间最大距离约束
     */
    public static boolean isRouteDistanceConstraintValid(List<SiteBean> sites, SiteBean depot) {

        ParameterBean parameter = TaskContext.getParameter();

//        // 检查起点到第一个站点的距离
//        double distanceFromDepot = TabuSearchUtil.getDistance(depot, sites.get(0));
//        if (distanceFromDepot > parameter.getBetweenMaximumDistance()) {
//            log.debug("起点到第一个站点距离 {}:.2f 超过最大间距限制 {}:.2f",
//                    distanceFromDepot, parameter.getBetweenMaximumDistance());
//            return false;
//        }

        // 检查相邻站点间的距离
        for (int i = 0; i < sites.size() - 1; i++) {
            SiteBean currentSite = sites.get(i);
            SiteBean nextSite = sites.get(i + 1);

            double distance = TabuSearchUtil.getDistance(currentSite, nextSite);
            if (distance > parameter.getBetweenMaximumDistance()) {
                return false;
            }
        }

//        // 如果启用返仓，检查最后一个站点到起点的距离
//        if (parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1 && !sites.isEmpty()) {
//            double distanceToDepot = TabuSearchUtil.getDistance(sites.get(sites.size() - 1), depot);
//            if (distanceToDepot > parameter.getBetweenMaximumDistance()) {
//                log.debug("最后站点到起点距离 {}:.2f 超过最大间距限制 {}:.2f",
//                        distanceToDepot, parameter.getBetweenMaximumDistance());
//                return false;
//            }
//        }

        return true;
    }


}
