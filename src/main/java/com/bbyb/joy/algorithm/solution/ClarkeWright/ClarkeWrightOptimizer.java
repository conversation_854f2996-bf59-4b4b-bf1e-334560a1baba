package com.bbyb.joy.algorithm.solution.ClarkeWright;

import cn.hutool.core.util.ObjectUtil;
import com.bbyb.joy.algorithm.bean.*;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import com.bbyb.joy.algorithm.util.SiteLinesUtil;
import com.bbyb.joy.algorithm.util.TimeWindowUtil;
import com.bbyb.joy.algorithm.util.VehicleUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.bbyb.joy.algorithm.solution.ClarkeWright.ClarkeWrightUtil.wouldCreateCrossing;

@Slf4j
public class ClarkeWrightOptimizer {
    public List<SiteLineBean> resolver(List<SiteBean> sites) {
        List<SiteBean> cwSites = sites.stream().map(ObjectUtil::cloneByStream).sorted(Comparator.<SiteBean, LocalTime>comparing(n -> LocalTime.parse(n.getOpenTime(), DateTimeFormatter.ofPattern("HH:mm")))).toList();
        log.debug("开始使用【节约算法 (<PERSON> & <PERSON>)】进行排线！");

        // 1. 获取上下文信息
        log.debug("获取上下文信息！");
        SiteBean beginSite = TaskContext.getBeginSite();
        ParameterBean parameter = TaskContext.getParameter();

        if (cwSites.isEmpty()) {
            log.debug("没有需要排线的站点，任务结束。");
            return new ArrayList<>();
        }
        // 2. 计算所有站点对的节约值
        log.debug("开始计算节约值 (K-近邻限制)...");
        HashMap<String, Double> depotMatrixMap = MatrixContext.getMatrixMap(beginSite.getCode());
        List<ClarkeWrightSaving> savingsList = new ArrayList<>();

        for (SiteBean siteI : cwSites) {
            HashMap<String, Double> siteIMatrixMap = MatrixContext.getMatrixMap(siteI.getCode());

            // 1. 找到 siteI 的 K 个最近邻居
            List<SiteBean> neighbors = new ArrayList<>(sites);
            neighbors.remove(siteI); // 移除自己
            neighbors.sort(Comparator.<SiteBean>comparingDouble(s -> siteIMatrixMap.getOrDefault(s.getCode(), Double.MAX_VALUE)).thenComparing(n -> LocalTime.parse(n.getOpenTime(), DateTimeFormatter.ofPattern("HH:mm")).getHour() + LocalTime.parse(n.getOpenTime(), DateTimeFormatter.ofPattern("HH:mm")).getMinute()));

            // 2. 只在 K 个近邻中计算节约值
            for (SiteBean siteJ : neighbors) {
                // 防止重复计算 (i,j) 和 (j,i)
                if (siteI.getCode().compareTo(siteJ.getCode()) > 0) continue;

                double distDepotToI = depotMatrixMap.getOrDefault(siteI.getCode(), Double.MAX_VALUE);
                double distDepotToJ = depotMatrixMap.getOrDefault(siteJ.getCode(), Double.MAX_VALUE);
                double distIToJ = siteIMatrixMap.getOrDefault(siteJ.getCode(), Double.MAX_VALUE);

                if (distDepotToI != Double.MAX_VALUE && distDepotToJ != Double.MAX_VALUE && distIToJ != Double.MAX_VALUE) {
                    // 计算节约值，考虑返仓逻辑
                    double savingValue = ClarkeWrightUtil.calculateSavingValue(siteI, siteJ, distDepotToI, distDepotToJ, distIToJ, beginSite, parameter);
                    savingsList.add(new ClarkeWrightSaving(siteI, siteJ, savingValue));
                }
            }
        }

        // 3. 按节约值降序排序
        savingsList.sort(Comparator.comparingDouble(ClarkeWrightSaving::value).reversed());

        log.debug("节约值排序完成。");

        log.debug("开始初始化线路，每个站点一条专线...");
        Map<String, SiteLineBean> siteToRouteMap = new HashMap<>();
        Set<SiteLineBean> finalRoutes = new HashSet<>();

        for (SiteBean site : sites) {
            SiteLineBean initialLine = new SiteLineBean();
            initialLine.refreshCode();
            initialLine.setBeginSite(beginSite);
            initialLine.setSites(new ArrayList<>());
            initialLine.getSites().add(site);

            VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeForFSMVRP(initialLine);
            if (vehicleType == null) {
                log.warn("站点 {} 本身不满足约束条件 没有车型满足线路运力装载，无法规划。", site.getCode());
                 continue;
            }
            initialLine.setVehicleType(vehicleType);
            initialLine.refreshMetaData();

            if (!SiteLinesUtil.isLineFeasible(vehicleType,initialLine)) {
                log.warn("站点 {} 本身不满足约束条件（超载或时间窗冲突），无法规划。", site.getCode());
                continue;
            }

            finalRoutes.add(initialLine);
            siteToRouteMap.put(site.getCode(), initialLine);
        }
        log.debug("线路初始化完成，共 {} 条初始线路。", finalRoutes.size());

        Set<ClarkeWrightSaving> availableSavings = new HashSet<>(savingsList);

        while (true) {
            ClarkeWrightSaving bestSaving = null;
            double maxScore = -Double.MAX_VALUE;

            for (ClarkeWrightSaving saving : availableSavings) {
                if (saving.value() <= 0) continue;

                SiteBean siteA = saving.siteA();
                SiteBean siteB = saving.siteB();
                SiteLineBean routeA = siteToRouteMap.get(siteA.getCode());
                SiteLineBean routeB = siteToRouteMap.get(siteB.getCode());

                if (routeA == null || routeB == null || routeA.equals(routeB)) {
                    log.debug("线路相同，不满足合并条件，跳过线路合并！");
                    continue;
                }
                if (!ClarkeWrightUtil.isEndpoint(siteA, routeA) || !ClarkeWrightUtil.isEndpoint(siteB, routeB)) {
                    log.debug("当前点位以及被其他点位合并！");
                    continue;
                }
                VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeForMergedLinesFSMVRP(routeA, routeB);
                if (vehicleType == null) {
                    log.debug("线路装载率超载，匹配车型失败，不满足合并条件，跳过线路合并！");
                    continue;
                }
//                if (ClarkeWrightUtil.isMergeOverloaded(routeA, routeB, vehicleType, parameter)) {
//                    log.debug("线路装载率超载，不满足合并条件，跳过线路合并！");
//                    continue;
//                }
                // 检查时间窗约束
                if (!TimeWindowUtil.isMergeTimeWindowFeasible(routeA, routeB, siteA)) {
                    log.debug("线路时间点位时间窗冲突，不满足合并条件，跳过线路合并！");
                    continue;
                }
                // 添加站点间距离约束检查
                if (!ClarkeWrightUtil.isRouteDistanceConstraintValid(routeA, routeB, siteA, siteB, parameter)) {
                    log.debug("合并后线路存在超过最大间距的站点对，跳过线路合并！");
                    continue;
                }
                // 线路交叉检查
                if (wouldCreateCrossing(routeA, routeB, siteA, siteB, finalRoutes)) {
                    log.debug("合并会产生线路交叉，跳过线路合并！");
                    continue;
                }

                if (saving.value() > maxScore) {
                    maxScore = saving.value();
                    bestSaving = saving;
                }
            }

            if (bestSaving == null) {
                log.debug("循环结束：没有更多可供合并的线路。");
                break;
            }

            // c. 执行合并逻辑
            availableSavings.remove(bestSaving); // 从待选集合中移除，避免重复使用

            SiteBean siteA = bestSaving.siteA();
            SiteBean siteB = bestSaving.siteB();
            SiteLineBean routeA = siteToRouteMap.get(siteA.getCode());
            SiteLineBean routeB = siteToRouteMap.get(siteB.getCode());

            log.debug("成功匹配最佳加权节约值：{} <-> {} (原始节约: {}, 得分: {})，执行合并。", siteA.getCode(), siteB.getCode(), String.format("%.2f", bestSaving.value()), String.format("%.2f", maxScore));

            // 您原来的智能合并逻辑，完全不变
            List<SiteBean> sitesA = routeA.getSites();
            List<SiteBean> sitesB = routeB.getSites();

            // 智能合并方向决策（新增）
            boolean isHeadA = sitesA.get(0).equals(siteA);
            boolean isTailA = sitesA.get(sitesA.size() - 1).equals(siteA);
            boolean isHeadB = sitesB.get(0).equals(siteB);
            boolean isTailB = sitesB.get(sitesB.size() - 1).equals(siteB);

            // 获取仓库距离矩阵
            HashMap<String, Double> depotMatrix = MatrixContext.getMatrixMap(beginSite.getCode());

            // 计算所有可能的合并方案及其起始点优劣
            List<MergeOptionBean> mergeOptions = new ArrayList<>();

            if (isTailA && isHeadB) {
                // Case 1: A尾 -> B头（A + B）
                List<SiteBean> merged1 = new ArrayList<>(sitesA);
                merged1.addAll(sitesB);
                double startDistance1 = depotMatrix.getOrDefault(merged1.get(0).getCode(), Double.MAX_VALUE);
                double totalDistance1 = ClarkeWrightUtil.calculateMergedRouteDistance(merged1, beginSite);
                mergeOptions.add(new MergeOptionBean(1, merged1, startDistance1, totalDistance1, "A尾->B头"));
            }

            if (isTailA && isTailB) {
                // Case 2: A尾 -> B尾（A + reverse(B)）
                List<SiteBean> merged2 = new ArrayList<>(sitesA);
                List<SiteBean> reversedB = new ArrayList<>(sitesB);
                Collections.reverse(reversedB);
                merged2.addAll(reversedB);
                double startDistance2 = depotMatrix.getOrDefault(merged2.get(0).getCode(), Double.MAX_VALUE);
                double totalDistance2 = ClarkeWrightUtil.calculateMergedRouteDistance(merged2, beginSite);
                mergeOptions.add(new MergeOptionBean(2, merged2, startDistance2, totalDistance2, "A尾->B尾(反转B)"));
            }

            if (isHeadA && isHeadB) {
                // Case 3: A头 -> B头（reverse(A) + B）
                List<SiteBean> reversedA = new ArrayList<>(sitesA);
                Collections.reverse(reversedA);
                List<SiteBean> merged3 = new ArrayList<>(reversedA);
                merged3.addAll(sitesB);
                double startDistance3 = depotMatrix.getOrDefault(merged3.get(0).getCode(), Double.MAX_VALUE);
                double totalDistance3 = ClarkeWrightUtil.calculateMergedRouteDistance(merged3, beginSite);
                mergeOptions.add(new MergeOptionBean(3, merged3, startDistance3, totalDistance3, "A头->B头(反转A)"));
            }

            if (isHeadA && isTailB) {
                // Case 4: A头 -> B尾（B + A）
                List<SiteBean> merged4 = new ArrayList<>(sitesB);
                merged4.addAll(sitesA);
                double startDistance4 = depotMatrix.getOrDefault(merged4.get(0).getCode(), Double.MAX_VALUE);
                double totalDistance4 = ClarkeWrightUtil.calculateMergedRouteDistance(merged4, beginSite);
                mergeOptions.add(new MergeOptionBean(4, merged4, startDistance4, totalDistance4, "A头->B尾(B+A)"));
            }

            // 过滤掉会产生交叉的合并方案
            List<MergeOptionBean> validOptions = mergeOptions.stream()
                    .filter(option -> !ClarkeWrightUtil.wouldCreateCrossingForOption(
                            option.getMergedSites(), finalRoutes, routeA, routeB))
                    .toList();

            // 如果所有方案都会产生交叉，跳过这次合并
            if (validOptions.isEmpty()) {
                log.debug("所有合并方案都会产生交叉，跳过合并：{} <-> {}", siteA.getCode(), siteB.getCode());
                continue;
            }

            // 从有效方案中选择最优的：起始点距离 + 总距离 + 时间窗适应性
            MergeOptionBean bestOption = validOptions.stream()
                    .min(Comparator.comparingDouble(ClarkeWrightUtil::calculateMergeScore))
                    .orElse(validOptions.get(0));

            // 应用最优合并方案
            sitesA.clear();
            sitesA.addAll(bestOption.getMergedSites());

            // 根据线路站点装载率加载车型(这里是重新匹配最优车型)
            VehicleTypeBean vehicleType = VehicleUtil.optimalVehicleTypeWithCostForSites(sitesA);
            if (vehicleType != null) {
                // 更新合并后的线路A
                routeA.setVehicleType(vehicleType);
                routeA.setSites(sitesA);
                routeA.refreshMetaData();

                // 更新B线路所有站点的归属
                for (SiteBean siteInB : sitesB) {
                    siteToRouteMap.put(siteInB.getCode(), routeA);
                }

                // 从最终结果中移除被合并的B线路
                finalRoutes.remove(routeB);
            }
        }

        return new ArrayList<>(finalRoutes);
    }
}
