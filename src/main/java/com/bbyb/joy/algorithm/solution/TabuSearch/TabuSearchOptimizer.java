package com.bbyb.joy.algorithm.solution.TabuSearch;

import cn.hutool.core.util.ObjectUtil;
import com.bbyb.joy.algorithm.bean.ParameterBean;
import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.context.TaskContext;
import lombok.extern.slf4j.Slf4j;

import java.awt.geom.Line2D;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TabuSearchOptimizer {

    //最大迭代次数
    private final int maxIterations;
    //禁忌期长度
    private final int tabuTenure;

    public TabuSearchOptimizer(int maxIterations, int tabuTenure) {
        this.maxIterations = maxIterations;
        this.tabuTenure = tabuTenure;
    }

    public List<SiteLineBean> resolver(List<SiteLineBean> initialSolution) {
        try {
            List<SiteLineBean> bestSolution = deepCloneSolution(initialSolution);
            List<SiteLineBean> currentSolution = deepCloneSolution(initialSolution);
            double bestCost = TabuSearchUtil.calculateTotalCost(bestSolution);
            double currentCost = bestCost;

            Map<String, Integer> tabuList = new HashMap<>();
            log.debug("开始禁忌搜索优化，初始方案成本：{} (含车辆成本)", String.format("%.2f", bestCost));

            for (int i = 0; i < maxIterations; i++) {

                ITabuSearchMove bestMove = findBestMove(currentSolution, tabuList, i, currentCost, bestCost);

                if (bestMove == null) {
                    log.debug("在第 {} 次迭代中未找到有效移动，搜索提前结束。", i);
                    break;
                }

                bestMove.apply();
                currentSolution.removeIf(line -> line.getSites().isEmpty());
                // 3. 更新禁忌列表
                String tabuKey = bestMove.getTabuKey();
                if (tabuKey != null) {
                    tabuList.put(tabuKey, i + tabuTenure);
                }
                // 清理过期的禁忌项（可定期清理，而非每次）
                if (i % 20 == 0) {
                    int finalI = i;
                    tabuList.entrySet().removeIf(entry -> entry.getValue() < finalI);
                }

                // 4. 更新最优解
                currentCost = TabuSearchUtil.calculateTotalCost(currentSolution);
                if (currentCost < bestCost) {
                    bestSolution = deepCloneSolution(currentSolution);
                    bestCost = currentCost;
                    log.info("找到更优解！迭代: {}, 新成本: {}, 线路数: {}", i, String.format("%.2f", bestCost), bestSolution.size());
                }

                if (i % 50 == 0 && i != 0) {
                    log.debug("迭代: {}, 当前成本: {}, 最优成本: {}", i, String.format("%.2f", currentCost), String.format("%.2f", bestCost));
                }
            }

            log.info("禁忌搜索完成！最终最优成本: {}, 线路数: {}", String.format("%.2f", bestCost), bestSolution.size());
            return bestSolution;
        } catch (Exception e) {
            log.error("禁忌搜索执行失败，错误原因：{}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private ITabuSearchMove findBestTwoOptMove(List<SiteLineBean> solution, ITabuSearchMove currentBestMove) {
        double bestCostChange = (currentBestMove == null) ? 0 : currentBestMove.getCostChange(); // 初始化为0，因为我们只找改善的

        for (SiteLineBean line : solution) {
            List<SiteBean> sites = line.getSites();
            int n = sites.size();
            if (n < 4) continue; // 2-Opt至少需要4个点

            // 检查线路是否有自交叉
            boolean hasSelfCrossing = hasSelfCrossing(line);

            // 优先处理有自交叉的线路
            if (hasSelfCrossing) {
                currentBestMove = fixSelfCrossing(line, bestCostChange, currentBestMove);
                if (currentBestMove != null && currentBestMove.getCostChange() < bestCostChange) {
                    bestCostChange = currentBestMove.getCostChange();
                }
            }

            for (int i = 0; i < n; i++) {
                for (int j = i + 1; j < n; j++) {

                    // 检查2-Opt操作后是否会违反最大间距约束
                    if (!TabuSearchUtil.isTwoOptDistanceConstraintValid(line, i, j)) {
                        continue;
                    }

                    double costChange = TabuSearchUtil.calculateTwoOptCostChange(line, i, j);

                    if (costChange < bestCostChange) {
                        bestCostChange = costChange;
                        currentBestMove = new TabuSearchTwoOptMove(line, i, j, costChange);
                    }
                }
            }
        }
        return currentBestMove;
    }

    /**
     * 检查线路是否有自交叉
     */
    private boolean hasSelfCrossing(SiteLineBean line) {
        List<SiteBean> sites = line.getSites();
        int n = sites.size();

        if (n < 4) return false;

        // 检查线路内部是否有交叉
        for (int i = 0; i < n - 1; i++) {
            for (int j = i + 2; j < n - 1; j++) {
                // 避免检查相邻边
                if (j == i + 1 || (i == 0 && j == n - 2)) continue;

                SiteBean p1 = sites.get(i);
                SiteBean p2 = sites.get(i + 1);
                SiteBean p3 = sites.get(j);
                SiteBean p4 = sites.get(j + 1);

                if (Line2D.linesIntersect(p1.getLon(), p1.getLat(), p2.getLon(), p2.getLat(),
                        p3.getLon(), p3.getLat(), p4.getLon(), p4.getLat())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 专门修复自交叉问题
     */
    private ITabuSearchMove fixSelfCrossing(SiteLineBean line, double currentBestCost, ITabuSearchMove currentBestMove) {
        List<SiteBean> sites = line.getSites();
        int n = sites.size();

        // 找到交叉的边对
        for (int i = 0; i < n - 1; i++) {
            for (int j = i + 2; j < n - 1; j++) {
                if (j == i + 1 || (i == 0 && j == n - 2)) continue;

                SiteBean p1 = sites.get(i);
                SiteBean p2 = sites.get(i + 1);
                SiteBean p3 = sites.get(j);
                SiteBean p4 = sites.get(j + 1);

                if (Line2D.linesIntersect(p1.getLon(), p1.getLat(), p2.getLon(), p2.getLat(),
                        p3.getLon(), p3.getLat(), p4.getLon(), p4.getLat())) {

                    if (!TabuSearchUtil.isTwoOptDistanceConstraintValid(line, i + 1, j)) {
                        log.debug("修复自交叉的2-Opt操作({},{})会违反距离约束，跳过", i + 1, j);
                        continue;
                    }

                    // 找到交叉，尝试2-Opt修复
                    double costChange = TabuSearchUtil.calculateTwoOptCostChange(line, i + 1, j);

                    if (costChange < currentBestCost) {
                        return new TabuSearchTwoOptMove(line, i + 1, j, costChange);
                    }
                }
            }
        }

        return currentBestMove;
    }


    /**
     * 交换移动 ( findBestSwapMove): 交换两条线路中的站点
     */
    private ITabuSearchMove findBestSwapMove(List<SiteLineBean> solution, Map<String, Integer> tabuList, int currentIteration, double currentCost, double bestCostSoFar, ITabuSearchMove currentBestMove) {
        double bestCostChange = (currentBestMove == null) ? Double.MAX_VALUE : currentBestMove.getCostChange();

        for (SiteLineBean line1 : solution) {
            for (int index1 = 0; index1 < line1.getSites().size(); index1++) {
                SiteBean site1 = line1.getSites().get(index1);

                // 使用现有方法找到邻近线路，而不是遍历所有线路
                List<SiteLineBean> nearbyLines = TabuSearchUtil.findNearbyLinesForSite(site1, solution, line1);

                for (SiteLineBean line2 : nearbyLines) {
                    for (int index2 = 0; index2 < line2.getSites().size(); index2++) {
                        SiteBean site2 = line2.getSites().get(index2);

                        // 1. 检查约束
                        if (!isSwapFeasible(line1, site1, line2, site2)) {
                            continue;
                        }

                        // 2. 交叉数量大于交换前不允许交换
                        if (TabuSearchUtil.calculateCrossingChangeForRelocateFlag(site1, line1, index1, line2, index2)) {
                            continue;
                        }

                        // 3. 计算成本变化
                        double costChange = TabuSearchUtil.calculateSwapCostChange(line1, index1, site1, line2, index2, site2);

                        // 4. 禁忌和特赦检查
                        String code1 = site1.getCode();
                        String code2 = site2.getCode();
                        String prospectiveTabuKey = code1.compareTo(code2) < 0 ? "swap_" + code1 + "_" + code2 : "swap_" + code2 + "_" + code1;
                        boolean isTabu = tabuList.containsKey(prospectiveTabuKey) && tabuList.get(prospectiveTabuKey) > currentIteration;

                        boolean aspiration = (currentCost + costChange) < bestCostSoFar;

                        if (costChange >= bestCostChange) continue;
                        if (isTabu && !aspiration) continue;

                        bestCostChange = costChange;
                        currentBestMove = new TabuSearchSwapMove(line1, site1, index1, line2, site2, index2, costChange);
                    }
                }
            }
        }
        return currentBestMove;
    }

    // 在 TabuSearchOptimizer 类中添加以下方法
    private boolean isSwapFeasible(SiteLineBean line1, SiteBean site1, SiteLineBean line2, SiteBean site2) {
        if (line1.getVehicleType() == null || line2.getVehicleType() == null) {
            return false;
        }

        // 检查最大站点数限制 - 交换不会改变站点数量，所以不需要额外检查
        // 但需要确保两条线路都没有超过限制
        ParameterBean parameter = TaskContext.getParameter();
        if (parameter != null && parameter.getMaximumSiteSize() != null) {
            if (line1.getSites().size() > parameter.getMaximumSiteSize() ||
                    line2.getSites().size() > parameter.getMaximumSiteSize()) {
                return false; // 任一线路超过最大站点数限制
            }
        }

        // 检查载重约束
        if ((line1.getTotalWeight() - site1.getWeight() + site2.getWeight()) > line1.getVehicleType().getLoadableWeight() ||
                (line1.getTotalVolume() - site1.getVolume() + site2.getVolume()) > line1.getVehicleType().getLoadableVolume()) {
            return false;
        }

        // 检查载重约束
        if ((line2.getTotalWeight() - site2.getWeight() + site1.getWeight()) > line2.getVehicleType().getLoadableWeight() ||
                (line2.getTotalVolume() - site2.getVolume() + site1.getVolume()) > line2.getVehicleType().getLoadableVolume()) {
            return false;
        }

        // 检查时间窗约束
        return true; // 时间窗检查在apply阶段进行
    }

    // 最后，在 findBestMove 方法中调用它
    private ITabuSearchMove findBestMove(List<SiteLineBean> currentSolution, Map<String, Integer> tabuList, int currentIteration, double currentCost, double bestCostSoFar) {
        ITabuSearchMove bestMove = null;

        bestMove = findBestRelocateMove(currentSolution, tabuList, currentIteration, currentCost, bestCostSoFar, bestMove);
        bestMove = findBestSwapMove(currentSolution, tabuList, currentIteration, currentCost, bestCostSoFar, bestMove);
        bestMove = findBestTwoOptMove(currentSolution, bestMove);

        // 线路内部路顺全局优化 - 每10次迭代执行一次
//        if (currentIteration % 10 == 0) {
            bestMove = findBestIntraRouteTSPMove(currentSolution, tabuList, currentIteration, currentCost, bestCostSoFar, bestMove);
//        }

        return bestMove;
    }


    /**
     * 寻找最佳的线路内部TSP优化移动
     * 对单条线路进行完整的旅行商问题求解
     */
    private static ITabuSearchMove findBestIntraRouteTSPMove(List<SiteLineBean> solution, Map<String, Integer> tabuList,
                                                             int currentIteration, double currentCost, double bestCostSoFar,
                                                             ITabuSearchMove currentBestMove) {
        double bestCostChange = (currentBestMove == null) ? Double.MAX_VALUE : currentBestMove.getCostChange();

        for (SiteLineBean line : solution) {
            List<SiteBean> sites = line.getSites();
            if (sites.size() < 4) continue; // 站点太少不需要优化

            // 计算当前线路距离
            double currentDistance = line.getTotalDistance();

            // 使用最近邻 + 2-opt求解TSP
            List<SiteBean> optimizedOrder = TabuSearchUtil.solveTSPForRoute(sites, line.getBeginSite());

            // 检查优化后的路径是否满足站点间距离约束
            if (!TabuSearchUtil.isRouteDistanceConstraintValid(optimizedOrder, line.getBeginSite())) {
                continue;
            }

            // 计算优化后的距离
            double optimizedDistance = TabuSearchUtil.calculateRouteDistance(optimizedOrder, line.getBeginSite());
            double costChange = optimizedDistance - currentDistance;

            if (costChange >= bestCostChange) continue;
            if (costChange >= -10.0) continue; // 改进太小就跳过

            // 禁忌检查
            String tabuKey = "intratsp_" + line.getCode() + "_" + TabuSearchUtil.generateOrderHash(optimizedOrder);
            boolean isTabu = tabuList.containsKey(tabuKey) && tabuList.get(tabuKey) > currentIteration;
            boolean aspiration = (currentCost + costChange) < bestCostSoFar;

            if (isTabu && !aspiration) continue;

            bestCostChange = costChange;
            currentBestMove = new TabuSearchIntraRouteTSPMove(line, optimizedOrder, costChange);
        }
        return currentBestMove;
    }



    /**
     * 在邻域中寻找最佳的重定位移动
     */
    private ITabuSearchMove findBestRelocateMove(List<SiteLineBean> solution, Map<String, Integer> tabuList, int currentIteration, double currentCost, double bestCostSoFar, ITabuSearchMove currentBestMove) {
        double bestCostChange = (currentBestMove == null) ? Double.MAX_VALUE : currentBestMove.getCostChange();

        for (SiteLineBean fromLine : solution) {
            for (int fromIndex = 0; fromIndex < fromLine.getSites().size(); fromIndex++) {
                SiteBean siteToMove = fromLine.getSites().get(fromIndex);

                //  跨线路重定位：只考虑邻近线路
                List<SiteLineBean> nearbyLines = TabuSearchUtil.findNearbyLinesForSite(siteToMove, solution, fromLine);

                for (SiteLineBean toLine : nearbyLines) {
                    if (!isRelocateFeasible(siteToMove, toLine)) {
                        continue;
                    }
                    boolean isInSameLine = fromLine == toLine;

                    for (int toIndex = 0; toIndex <= toLine.getSites().size(); toIndex++) {
                        // 如果是在同一条路线内移动，要防止移动到原位置附近导致无效操作
                        if (isInSameLine && toIndex == fromIndex) {
                            continue;
                        }
                        // 交叉数量大于交换前不允许交换
                        if(TabuSearchUtil.calculateCrossingChangeForRelocateFlag(siteToMove, fromLine, fromIndex, toLine, toIndex)){
                            continue;
                        }
                        double costChange = TabuSearchUtil.calculateRelocateCostChange(siteToMove, fromLine, fromIndex, toLine, toIndex);

                        String prospectiveTabuKey = "relocate_" + siteToMove.getCode() + "_to_" + fromLine.getCode();
                        boolean isTabu = tabuList.containsKey(prospectiveTabuKey) && tabuList.get(prospectiveTabuKey) > currentIteration;

                        boolean aspiration = (currentCost + costChange) < bestCostSoFar;

                        if (costChange >= bestCostChange) continue;
                        if (isTabu && !aspiration) continue;

                        bestCostChange = costChange;
                        currentBestMove = new TabuSearchRelocateMove(siteToMove, fromLine, toLine, fromIndex, toIndex, costChange);
                    }
                }
            }
        }
        return currentBestMove;
    }

    private boolean isRelocateFeasible(SiteBean siteToMove, SiteLineBean toLine) {
        if (toLine.getVehicleType() == null) {
            return false;
        }

        // 检查最大站点数限制 - 强制约束
        ParameterBean parameter = TaskContext.getParameter();
        if (parameter != null && parameter.getMaximumSiteSize() != null) {
            if (toLine.getSites().size() >= parameter.getMaximumSiteSize()) {
                return false; // 目标线路已达到最大站点数，禁止添加
            }
        }

        // 检查载重约束
        return !((toLine.getTotalWeight() + siteToMove.getWeight()) > toLine.getVehicleType().getLoadableWeight()) &&
                !((toLine.getTotalVolume() + siteToMove.getVolume()) > toLine.getVehicleType().getLoadableVolume());// 时间窗检查在apply阶段进行
    }

    private List<SiteLineBean> deepCloneSolution(List<SiteLineBean> solution) {
        return solution.stream().map(ObjectUtil::cloneByStream).collect(Collectors.toList());
    }




}