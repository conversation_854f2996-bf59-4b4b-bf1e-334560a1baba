package com.bbyb.joy.algorithm.solution.ClarkeWright;

import com.bbyb.joy.algorithm.bean.*;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class ClarkeWrightUtil {
    /**
     * 检查一个站点是否是其所在线路的端点
     */
    static boolean isEndpoint(SiteBean site, SiteLineBean line) {
        if (line.getSites().isEmpty()) {
            return false;
        }
        // 如果线路只有一个点，那它既是开头也是结尾，是端点
        return line.getSites().get(0).equals(site) || line.getSites().get(line.getSites().size() - 1).equals(site);
    }

    /**
     * 检查两条线路合并后是否超限
     */
    static boolean isMergeOverloaded(SiteLineBean routeA, SiteLineBean routeB, VehicleTypeBean vehicleType, ParameterBean parameter) {
        // 检查合并后的总站点数
        if (routeA.getSites().size() + routeB.getSites().size() > parameter.getMaximumSiteSize()) {
            return true;
        }
        // 检查合并后的总重量
        if (Double.compare(routeA.getTotalWeight() + routeB.getTotalWeight(), vehicleType.getLoadableWeight()) > 0) {
            return true;
        }
        // 检查合并后的总体积
        return Double.compare(routeA.getTotalVolume() + routeB.getTotalVolume(), vehicleType.getLoadableVolume()) > 0;
    }


    /**
     * 检查使用指定车型合并两条线路是否可行
     */
    static boolean isMergeValidWithVehicleType(SiteLineBean routeA, SiteLineBean routeB,
                                               VehicleTypeBean vehicleType, ParameterBean parameter) {
        // 检查站点数量约束
        if (routeA.getSites().size() + routeB.getSites().size() > parameter.getMaximumSiteSize()) {
            return false;
        }

        // 检查载重约束
        double totalWeight = routeA.getTotalWeight() + routeB.getTotalWeight();
        double totalVolume = routeA.getTotalVolume() + routeB.getTotalVolume();

        return vehicleType.getLoadableWeight() >= totalWeight &&
                vehicleType.getLoadableVolume() >= totalVolume;
    }


    /**
     * 检查合并是否会产生交叉
     * @param routeA 线路A
     * @param routeB 线路B
     * @param siteA 连接点A
     * @param siteB 连接点B
     * @param allRoutes 当前所有线路
     * @return true表示会产生交叉，false表示不会
     */
    public static boolean wouldCreateCrossing(SiteLineBean routeA, SiteLineBean routeB,
                                        SiteBean siteA, SiteBean siteB,
                                        Set<SiteLineBean> allRoutes) {

        // 1. 生成所有可能的合并方案（与实际合并逻辑一致）
        List<List<SiteBean>> allMergeOptions = generateAllMergeOptions(routeA, routeB, siteA, siteB);

        // 2. 检查是否所有合并方案都会产生交叉
        boolean allOptionsHaveCrossing = true;

        for (List<SiteBean> mergedSites : allMergeOptions) {
            boolean currentOptionHasCrossing = false;

            // 检查当前合并方案是否与其他现有线路交叉
            for (SiteLineBean existingRoute : allRoutes) {
                // 跳过即将被合并的两条线路
                if (existingRoute != routeA && existingRoute != routeB) {
                    // 使用优化版交叉检测
                    if (hasLineCrossingOptimized(mergedSites, existingRoute.getSites())) {
                        currentOptionHasCrossing = true;
                        break; // 当前方案有交叉，检查下一个方案
                    }
                }
            }

            // 如果找到一个无交叉的方案，就允许合并
            if (!currentOptionHasCrossing) {
                allOptionsHaveCrossing = false;
                break;
            }
        }

        return allOptionsHaveCrossing; // 只有所有方案都有交叉才拒绝合并
    }

    /**
     * 生成所有可能的合并方案
     * 与ClarkeWrightOptimizer中的合并逻辑保持一致
     */
    private static List<List<SiteBean>> generateAllMergeOptions(SiteLineBean routeA, SiteLineBean routeB,
                                                          SiteBean siteA, SiteBean siteB) {
        List<List<SiteBean>> mergeOptions = new ArrayList<>();

        List<SiteBean> sitesA = new ArrayList<>(routeA.getSites());
        List<SiteBean> sitesB = new ArrayList<>(routeB.getSites());

        // 判断连接点在线路中的位置（头部或尾部）
        boolean isHeadA = sitesA.get(0).equals(siteA);
        boolean isTailA = sitesA.get(sitesA.size() - 1).equals(siteA);
        boolean isHeadB = sitesB.get(0).equals(siteB);
        boolean isTailB = sitesB.get(sitesB.size() - 1).equals(siteB);

        if (isTailA && isHeadB) {
            // Case 1: A尾 -> B头（A + B）
            List<SiteBean> merged1 = new ArrayList<>(sitesA);
            merged1.addAll(sitesB);
            mergeOptions.add(merged1);
        }

        if (isTailA && isTailB) {
            // Case 2: A尾 -> B尾（A + reverse(B)）
            List<SiteBean> merged2 = new ArrayList<>(sitesA);
            List<SiteBean> reversedB = new ArrayList<>(sitesB);
            Collections.reverse(reversedB);
            merged2.addAll(reversedB);
            mergeOptions.add(merged2);
        }

        if (isHeadA && isHeadB) {
            // Case 3: A头 -> B头（reverse(A) + B）
            List<SiteBean> reversedA = new ArrayList<>(sitesA);
            Collections.reverse(reversedA);
            List<SiteBean> merged3 = new ArrayList<>(reversedA);
            merged3.addAll(sitesB);
            mergeOptions.add(merged3);
        }

        if (isHeadA && isTailB) {
            // Case 4: A头 -> B尾（B + A）
            List<SiteBean> merged4 = new ArrayList<>(sitesB);
            merged4.addAll(sitesA);
            mergeOptions.add(merged4);
        }

        return mergeOptions;
    }

    /**
     * 检查特定的合并方案是否会与其他线路产生交叉
     *
     * @param mergedSites 合并后的站点序列
     * @param allRoutes 所有现有线路
     * @param routeA 被合并的线路A
     * @param routeB 被合并的线路B
     * @return true表示会产生交叉，false表示不会
     */
    public static boolean wouldCreateCrossingForOption(List<SiteBean> mergedSites,
                                                 Set<SiteLineBean> allRoutes,
                                                 SiteLineBean routeA,
                                                 SiteLineBean routeB) {
        // 检查合并方案是否与其他现有线路交叉
        for (SiteLineBean existingRoute : allRoutes) {
            // 跳过即将被合并的两条线路
            if (existingRoute != routeA && existingRoute != routeB) {
                // 使用优化版交叉检测
                if (hasLineCrossingOptimized(mergedSites, existingRoute.getSites())) {
                    return true; // 发现交叉
                }
            }
        }
        return false; // 无交叉
    }

    /**
     * 模拟合并操作，返回合并后的站点序列
     * 这里复用现有的智能合并逻辑
     */
    private static List<SiteBean> simulateMerge(SiteLineBean routeA, SiteLineBean routeB,
                                         SiteBean siteA, SiteBean siteB) {

        // 复制站点列表，避免影响原始数据
        List<SiteBean> sitesA = new ArrayList<>(routeA.getSites());
        List<SiteBean> sitesB = new ArrayList<>(routeB.getSites());

        // 判断连接点在线路中的位置（头部或尾部）
        boolean isHeadA = sitesA.get(0).equals(siteA);
        boolean isTailA = sitesA.get(sitesA.size() - 1).equals(siteA);
        boolean isHeadB = sitesB.get(0).equals(siteB);
        boolean isTailB = sitesB.get(sitesB.size() - 1).equals(siteB);

        List<SiteBean> merged = new ArrayList<>();

        // 智能合并方向决策（与现有逻辑保持一致）
        if (isTailA && isHeadB) {
            // Case 1: A尾 -> B头（最优直连）
            merged.addAll(sitesA);
            merged.addAll(sitesB);
        } else if (isTailA && isTailB) {
            // Case 2: A尾 -> B尾（反转B）
            merged.addAll(sitesA);
            Collections.reverse(sitesB);
            merged.addAll(sitesB);
        } else if (isHeadA && isHeadB) {
            // Case 3: A头 -> B头（反转A）
            Collections.reverse(sitesA);
            merged.addAll(sitesA);
            merged.addAll(sitesB);
        } else if (isHeadA && isTailB) {
            // Case 4: A头 -> B尾（B+A）
            merged.addAll(sitesB);
            merged.addAll(sitesA);
        }

        return merged;
    }

    /**
     * 优化版交叉检测 - 两阶段检测
     * 第一阶段：边界框快速过滤
     * 第二阶段：精确线段相交检测
     */
    private static boolean hasLineCrossingOptimized(List<SiteBean> sites1, List<SiteBean> sites2) {
        // 线路至少需要2个点才能形成线段
        if (sites1.size() < 2 || sites2.size() < 2) {
            return false;
        }

        // 第一阶段：边界框快速过滤
        if (!boundingBoxesOverlap(sites1, sites2)) {
            return false; // 边界框不重叠，必然无交叉
        }

        // 第二阶段：详细交叉检测
        return hasLineCrossing(sites1, sites2);
    }

    /**
     * 检查两条线路是否存在交叉
     * 使用线段相交算法进行几何判断
     */
    public static boolean hasLineCrossing(List<SiteBean> sites1, List<SiteBean> sites2) {
        // 线路至少需要2个点才能形成线段
        if (sites1.size() < 2 || sites2.size() < 2) {
            return false;
        }

        // 遍历线路1的所有线段
        for (int i = 0; i < sites1.size() - 1; i++) {
            SiteBean p1 = sites1.get(i);     // 线段1起点
            SiteBean p2 = sites1.get(i + 1); // 线段1终点

            // 遍历线路2的所有线段
            for (int j = 0; j < sites2.size() - 1; j++) {
                SiteBean p3 = sites2.get(j);     // 线段2起点
                SiteBean p4 = sites2.get(j + 1); // 线段2终点

                // 使用Java内置的线段相交算法
                if (java.awt.geom.Line2D.linesIntersect(
                        p1.getLon(), p1.getLat(), p2.getLon(), p2.getLat(),
                        p3.getLon(), p3.getLat(), p4.getLon(), p4.getLat())) {
                    return true; // 发现交叉
                }
            }
        }
        return false; // 无交叉
    }


    /**
     * 计算站点列表的边界框
     * @param sites 站点列表
     * @return 边界框数组 [minLon, minLat, maxLon, maxLat]
     */
    private static double[] calculateBoundingBox(List<SiteBean> sites) {
        if (sites.isEmpty()) {
            return new double[]{0, 0, 0, 0};
        }

        double minLon = sites.stream().mapToDouble(SiteBean::getLon).min().orElse(0);
        double minLat = sites.stream().mapToDouble(SiteBean::getLat).min().orElse(0);
        double maxLon = sites.stream().mapToDouble(SiteBean::getLon).max().orElse(0);
        double maxLat = sites.stream().mapToDouble(SiteBean::getLat).max().orElse(0);

        return new double[]{minLon, minLat, maxLon, maxLat};
    }

    /**
     * 检查两个边界框是否重叠
     * @param bbox1 边界框1 [minLon, minLat, maxLon, maxLat]
     * @param bbox2 边界框2 [minLon, minLat, maxLon, maxLat]
     * @return true表示重叠，false表示不重叠
     */
    private static boolean boundingBoxesOverlap(double[] bbox1, double[] bbox2) {
        // 如果任一维度不重叠，则整体不重叠
        return !(bbox1[2] < bbox2[0] ||  // bbox1的maxLon < bbox2的minLon
                bbox2[2] < bbox1[0] ||   // bbox2的maxLon < bbox1的minLon
                bbox1[3] < bbox2[1] ||   // bbox1的maxLat < bbox2的minLat
                bbox2[3] < bbox1[1]);    // bbox2的maxLat < bbox1的minLat
    }

    /**
     * 优化版边界框重叠检查
     */
    private static boolean boundingBoxesOverlap(List<SiteBean> sites1, List<SiteBean> sites2) {
        double[] bbox1 = calculateBoundingBox(sites1);
        double[] bbox2 = calculateBoundingBox(sites2);

        return boundingBoxesOverlap(bbox1, bbox2);
    }






    /**
     * 计算合并方案的综合得分（越小越好）
     */
    public static double calculateMergeScore(MergeOptionBean option) {
        double startWeight = 0.4;      // 起始点距离权重
        double totalWeight = 0.4;      // 总距离权重
        double timeWeight = 0.2;       // 时间窗适应性权重

        // 起始点距离得分（标准化）
        double startScore = option.getStartDistance() / 1000.0; // 假设最大距离1000

        // 总距离得分（标准化）
        double totalScore = option.getTotalDistance() / 10000.0; // 假设最大总距离10000

        // 时间窗适应性得分
        double timeScore = calculateTimeWindowScore(option.getMergedSites());

        return startWeight * startScore + totalWeight * totalScore + timeWeight * timeScore;
    }

    /**
     * 计算合并后线路的总距离
     * 根据enableReturn配置决定是否包含返仓距离
     */
    public static double calculateMergedRouteDistance(List<SiteBean> sites, SiteBean depot) {
        if (sites.isEmpty()) return 0.0;

        double totalDistance = 0.0;
        Map<String, Double> depotMatrix = MatrixContext.getMatrixMap(depot.getCode());

        // 从仓库到第一个站点
        totalDistance += depotMatrix.getOrDefault(sites.get(0).getCode(), 0.0);

        // 站点间距离
        for (int i = 0; i < sites.size() - 1; i++) {
            HashMap<String, Double> matrix = MatrixContext.getMatrixMap(sites.get(i).getCode());
            totalDistance += matrix.getOrDefault(sites.get(i + 1).getCode(), 0.0);
        }

        // 根据enableReturn配置决定是否包含返仓距离
        ParameterBean parameter = TaskContext.getParameter();
        if (parameter != null && parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1) {
            HashMap<String, Double> lastSiteMatrix = MatrixContext.getMatrixMap(sites.get(sites.size() - 1).getCode());
            totalDistance += lastSiteMatrix.getOrDefault(depot.getCode(), 0.0);
        }

        return totalDistance;
    }

    /**
     * 计算时间窗适应性得分（越小越好）
     */
    private static double calculateTimeWindowScore(List<SiteBean> sites) {
        if (sites.size() <= 1) return 0.0;

        double penalty = 0.0;

        // 检查相邻站点的时间窗是否合理
        for (int i = 0; i < sites.size() - 1; i++) {
            SiteBean current = sites.get(i);
            SiteBean next = sites.get(i + 1);

            // 解析时间窗
            try {
                LocalTime currentOpen = LocalTime.parse(current.getOpenTime(), DateTimeFormatter.ofPattern("HH:mm"));
                LocalTime nextOpen = LocalTime.parse(next.getOpenTime(), DateTimeFormatter.ofPattern("HH:mm"));

                // 如果下一个站点的开门时间早于当前站点，增加惩罚
                if (nextOpen.isBefore(currentOpen)) {
                    penalty += 60.0; // 时间倒序惩罚
                }

                // 如果时间间隔过大，也增加轻微惩罚
                long minutesDiff = Math.abs(Duration.between(currentOpen, nextOpen).toMinutes());
                if (minutesDiff > 120) { // 超过2小时
                    penalty += minutesDiff / 60.0; // 按小时计算惩罚
                }

            } catch (Exception e) {
                // 时间解析失败，给予轻微惩罚
                penalty += 10.0;
            }
        }

        return penalty;
    }


    /**
     * 计算节约值，考虑返仓逻辑
     *
     * @param siteI 站点I
     * @param siteJ 站点J
     * @param distDepotToI 仓库到站点I的距离
     * @param distDepotToJ 仓库到站点J的距离
     * @param distIToJ 站点I到站点J的距离
     * @param depot 仓库站点
     * @param parameter 参数配置
     * @return 节约值
     */
    public static double calculateSavingValue(SiteBean siteI, SiteBean siteJ,
                                        double distDepotToI, double distDepotToJ, double distIToJ,
                                        SiteBean depot, ParameterBean parameter) {

        // 基础节约值：S(i,j) = d(depot,i) + d(depot,j) - d(i,j)
        double basicSaving = distDepotToI + distDepotToJ - distIToJ;

        // 如果不需要返仓，直接返回基础节约值
        if (parameter.getEnableReturn() == null || parameter.getEnableReturn() == 0) {
            return basicSaving;
        }

        // 考虑返仓的节约值计算
        // 原来两条独立路径的返仓距离
        HashMap<String, Double> siteIMatrix = MatrixContext.getMatrixMap(siteI.getCode());
        HashMap<String, Double> siteJMatrix = MatrixContext.getMatrixMap(siteJ.getCode());
        double distIToDepot = siteIMatrix.getOrDefault(depot.getCode(), Double.MAX_VALUE);
        double distJToDepot = siteJMatrix.getOrDefault(depot.getCode(), Double.MAX_VALUE);

        if (distIToDepot == Double.MAX_VALUE || distJToDepot == Double.MAX_VALUE) {
            return basicSaving; // 如果无法获取返仓距离，使用基础节约值
        }

        // 合并后只需要一次返仓，节省了一次返仓距离
        // 增强节约值 = 基础节约值 + 节省的返仓距离
        // 这里假设合并后是 depot -> i -> j -> depot 的路径
        double returnSaving = distIToDepot; // 节省了从i返回仓库的距离

        return basicSaving + returnSaving;
    }


    /**
     * 检查合并后的线路是否满足站点间最大间距约束
     */
    public static boolean isRouteDistanceConstraintValid(SiteLineBean routeA, SiteLineBean routeB,
                                                   SiteBean siteA, SiteBean siteB, ParameterBean parameter) {
        // 模拟合并操作，获取合并后的站点序列
        List<SiteBean> mergedSites = simulateMerge(routeA, routeB, siteA, siteB);

        // 检查合并后线路中相邻站点间的距离
        for (int i = 0; i < mergedSites.size() - 1; i++) {
            SiteBean currentSite = mergedSites.get(i);
            SiteBean nextSite = mergedSites.get(i + 1);

            HashMap<String, Double> currentSiteMatrix = MatrixContext.getMatrixMap(currentSite.getCode());
            double distance = currentSiteMatrix.getOrDefault(nextSite.getCode(), Double.MAX_VALUE);

            if (distance > parameter.getBetweenMaximumDistance()) {
                return false;
            }
        }

        return true;
    }


}
