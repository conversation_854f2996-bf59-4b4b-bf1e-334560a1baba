package com.bbyb.joy.algorithm.context;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.util.GEOUtil;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 矩阵的上下文信息
 */
public class MatrixContext {
    private static final InheritableThreadLocal<HashMap<String, HashMap<String, Double>>> localMatrixMap = new InheritableThreadLocal<>();

    public static void refreshMatrixMap(List<SiteBean> sites, Map<String, Map<String, Double>> originalMatrixMap) {
        HashMap<String, HashMap<String, Double>> tmpMatrixMap = new HashMap<>();

        for (SiteBean site : sites) {

            Map<String, Double> originalCurSiteMatrix = originalMatrixMap.getOrDefault(site.getCode(), Collections.emptyMap());

            List<SiteBean> pairValue = sites.stream().filter(s -> !s.getCode().equals(site.getCode())).toList();
            HashMap<String, Double> matrixMap = new HashMap<>();
            for (SiteBean pairSite : pairValue) {

                double distance = originalCurSiteMatrix.getOrDefault(site.getCode(), GEOUtil.CalcEarthTwoPointDistance(site.getLon(), site.getLat(), pairSite.getLon(), pairSite.getLat()));

                matrixMap.put(pairSite.getCode(), distance);
            }
            tmpMatrixMap.put(site.getCode(), matrixMap);
        }

        localMatrixMap.set(tmpMatrixMap);
    }

    public static HashMap<String, Double> getMatrixMap(String code) {
        return localMatrixMap.get().computeIfAbsent(code, k -> new HashMap<>());
    }
}
