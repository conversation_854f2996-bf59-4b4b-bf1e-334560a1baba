package com.bbyb.joy.algorithm.context;

import cn.hutool.core.util.IdUtil;
import com.bbyb.joy.algorithm.bean.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 排线任务的上下文信息
 */
public class TaskContext {
    private static final InheritableThreadLocal<String> localTaskCode = new InheritableThreadLocal<>();
    private static final InheritableThreadLocal<ParameterBean> localParameter = new InheritableThreadLocal<>();
    private static final InheritableThreadLocal<List<VehicleTypeBean>>  localVehicleTypes = new InheritableThreadLocal<>();
    private static final InheritableThreadLocal<SiteBean> localBeginSite = new InheritableThreadLocal<>();
    private static final InheritableThreadLocal<List<SiteBean>> localFreeDestinationSites = new InheritableThreadLocal<>();
    private static final InheritableThreadLocal<List<SiteLineBean>> localCompleteSiteLines = new InheritableThreadLocal<>();

    /**
     * 生成一个 UUID 作为当前任务的标识
     */
    public static void initTaskCode() {
        String taskCode = IdUtil.simpleUUID();
        localTaskCode.set(taskCode);
    }

    /**
     * 获取当前任务的标识
     *
     * @return UUID 格式的任务标识
     */
    public static String getTaskCode() {
        return localTaskCode.get();
    }

    /**
     * 获取上下文的排线参数信息
     *
     * @return 排线参数
     */
    public static ParameterBean getParameter() {
        return localParameter.get();
    }

    /**
     * 设置上下文排线参数信息
     *
     * @param parameter 排线参数
     */
    public static void setParameter(ParameterBean parameter) {
        localParameter.set(parameter);
    }

    /**
     * 获取上下文设置的运力信息
     *
     * @return 车辆类型
     */
    public static List<VehicleTypeBean> getVehicleTypes() {
        return localVehicleTypes.get();
    }

    /**
     * 设置上下文设置的运力信息
     * @param vehicleTypes 车型信息
     */
    public static void setVehicleTypes(List<VehicleTypeBean> vehicleTypes) {
        localVehicleTypes.set(vehicleTypes);
    }

    /**
     * 获取上下文设置的起始站点信息
     *
     * @return 起始站点信息
     */
    public static SiteBean getBeginSite() {
        return localBeginSite.get();
    }

    /**
     * 上下文设置起始站点信息
     *
     * @param beginSite 起始站点
     */
    public static void setBeginSite(SiteBean beginSite) {
        localBeginSite.set(beginSite);
    }

    /**
     * 获取空闲的目的站点列表
     *
     * @return 空闲的目的站点
     */
    public static List<SiteBean> getFreeDestinationSites() {
        List<SiteBean> tmpDestinationSites = localFreeDestinationSites.get();
        if (tmpDestinationSites == null) {
            return new ArrayList<>();
        }
        return localFreeDestinationSites.get();
    }

    /**
     * 上下文设置空闲目的站点
     *
     * @param freeDestinationSites 空闲目的地站点
     */
    public static void setFreeDestinationSites(List<SiteBean> freeDestinationSites) {
        localFreeDestinationSites.set(freeDestinationSites);
    }

    /**
     * 添加一个空闲站点到上下文内
     *
     * @param freeDestinationSite 空闲站点
     */
    public static void addFreeDestinationSite(SiteBean freeDestinationSite) {
        List<SiteBean> tmpDestinationSites = localFreeDestinationSites.get();
        if (tmpDestinationSites == null) {
            tmpDestinationSites = new ArrayList<>();
        }
        tmpDestinationSites.add(freeDestinationSite);
        localFreeDestinationSites.set(tmpDestinationSites);
    }

    /**
     * 添加多个空闲站点到上下文内
     *
     * @param freeDestinationSites 空闲站点集合
     */
    public static void addFreeDestinationSites(List<SiteBean> freeDestinationSites) {
        List<SiteBean> tmpDestinationSites = localFreeDestinationSites.get();
        if (tmpDestinationSites == null) {
            tmpDestinationSites = new ArrayList<>();
        }
        tmpDestinationSites.addAll(freeDestinationSites);
        localFreeDestinationSites.set(tmpDestinationSites);
    }

    /**
     * 从上下文删除一个空闲站点
     *
     * @param freeDestinationSite 空闲站点
     */
    public static void removeFreeDestinationSites(SiteBean freeDestinationSite) {
        List<SiteBean> tmpDestinationSites = localFreeDestinationSites.get();
        if (tmpDestinationSites == null) return;
        tmpDestinationSites = tmpDestinationSites.stream().filter(siteBean -> !freeDestinationSite.getCode().equals(siteBean.getCode())).toList();
        localFreeDestinationSites.set(tmpDestinationSites);
    }

    /**
     * 从上下文删除空闲站点
     *
     * @param freeDestinationSites 空闲站点集合
     */
    public static void removeFreeDestinationSites(List<SiteBean> freeDestinationSites) {
        List<String> freeDestinationSiteCodes = freeDestinationSites.stream().map(SiteBean::getCode).toList();
        List<SiteBean> tmpDestinationSites = localFreeDestinationSites.get();
        if (tmpDestinationSites == null) {
            tmpDestinationSites = new ArrayList<>();
        }
        tmpDestinationSites = tmpDestinationSites.stream().filter(siteBean -> !freeDestinationSiteCodes.contains(siteBean.getCode())).toList();
        localFreeDestinationSites.set(tmpDestinationSites);
    }

    /**
     * 获取上下文中已完成的排线线路，供优化时使用
     *
     * @return 排线线路列表
     */
    public static List<SiteLineBean> getCompleteSiteLines() {
        List<SiteLineBean> tmpCompleteSiteLines = localCompleteSiteLines.get();
        return Objects.requireNonNullElseGet(tmpCompleteSiteLines, ArrayList::new);
    }

    /**
     * 上下文设置已排线的线路
     */
    public static void setCompleteSiteLines(List<SiteLineBean> completeSiteLines) {
        if (completeSiteLines == null) return;
        localCompleteSiteLines.set(new ArrayList<>(completeSiteLines));
    }

    /**
     * 上下文添加一个线路信息
     *
     * @param completeSiteLine 完成线路
     */
    public static void addCompleteSiteLine(SiteLineBean completeSiteLine) {
        List<SiteLineBean> tmpCompleteSiteLines = localCompleteSiteLines.get();
        if (tmpCompleteSiteLines == null) {
            tmpCompleteSiteLines = new ArrayList<>();
        } else {
            tmpCompleteSiteLines = new ArrayList<>(tmpCompleteSiteLines);
        }
        tmpCompleteSiteLines.add(completeSiteLine);
        localCompleteSiteLines.set(tmpCompleteSiteLines);
    }

    /**
     * 上下文添加多个线路信息
     *
     * @param completeSiteLines 完成线路集合
     */
    public static void addCompleteSiteLines(List<SiteLineBean> completeSiteLines) {
        List<SiteLineBean> tmpCompleteSiteLines = localCompleteSiteLines.get();
        if (tmpCompleteSiteLines == null) {
            tmpCompleteSiteLines = new ArrayList<>();
        } else {
            tmpCompleteSiteLines = new ArrayList<>(tmpCompleteSiteLines);
        }
        tmpCompleteSiteLines.addAll(completeSiteLines);
        localCompleteSiteLines.set(tmpCompleteSiteLines);
    }


    /**
     * 从上下文删除一个线路
     *
     * @param completeSiteLine 线路
     */
    public static void removeCompleteSiteLine(SiteLineBean completeSiteLine) {
        List<SiteLineBean> tmpCompleteSiteLines = localCompleteSiteLines.get();
        if (tmpCompleteSiteLines == null) {
            tmpCompleteSiteLines = new ArrayList<>();
        }
        tmpCompleteSiteLines = new ArrayList<>(tmpCompleteSiteLines.stream().filter(siteBean -> !completeSiteLine.getCode().equals(siteBean.getCode())).toList());
        localCompleteSiteLines.set(tmpCompleteSiteLines);
    }

    /**
     * 从上下文删除线路
     *
     * @param completeSiteLines 线路集合
     */
    public static void removeCompleteSiteLines(List<SiteLineBean> completeSiteLines) {
        List<String> tmpCompleteSiteLineCodes = completeSiteLines.stream().map(SiteLineBean::getCode).toList();
        List<SiteLineBean> tmpCompleteSiteLines = localCompleteSiteLines.get();
        if (tmpCompleteSiteLines == null) {
            return;
        }
        tmpCompleteSiteLines = new ArrayList<>(tmpCompleteSiteLines.stream().filter(siteBean -> !tmpCompleteSiteLineCodes.contains(siteBean.getCode())).toList());
        localCompleteSiteLines.set(tmpCompleteSiteLines);
    }

    public static void clear() {
        localTaskCode.remove();
        localParameter.remove();
        localVehicleTypes.remove();
        localBeginSite.remove();
        localFreeDestinationSites.remove();
        localCompleteSiteLines.remove();
    }
}
