package com.bbyb.joy.algorithm.implementation;

import com.bbyb.joy.algorithm.bean.SiteBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.Clusterable;
import org.apache.commons.math3.ml.clustering.KMeansPlusPlusClusterer;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * K-Means++ 聚类算法实现
 */
@Slf4j
public class KMeansPlusPlusImplementation {
    private final KMeansPlusPlusClusterer<KMeansPlusPlusCluster> cluster;

    public KMeansPlusPlusImplementation(int areaCount) {
        cluster = new KMeansPlusPlusClusterer<>(areaCount);
    }

    public List<List<SiteBean>> resolve(List<SiteBean> lineSites) {
        log.debug("K-Means 聚类开始:初始点数:{}", lineSites.size());
        Set<KMeansPlusPlusCluster> points = lineSites.stream().map(site -> new KMeansPlusPlusCluster(new double[]{site.getLon(), site.getLat()}, site)).collect(Collectors.toSet());

        List<CentroidCluster<KMeansPlusPlusCluster>> clusterSplit = cluster.cluster(points);
        return clusterSplit.stream().map(c -> c.getPoints().stream().map(KMeansPlusPlusCluster::site).toList()).toList();
    }


    /**
     * 坐标点信息
     */
    private record KMeansPlusPlusCluster(double[] points, SiteBean site) implements Clusterable {
        @Override
        public double[] getPoint() {
            return this.points;
        }
    }
}
