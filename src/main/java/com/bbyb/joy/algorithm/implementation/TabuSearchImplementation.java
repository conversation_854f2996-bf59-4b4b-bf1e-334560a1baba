package com.bbyb.joy.algorithm.implementation;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.util.GEOUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Random;

/**
 * 禁忌搜索算法实现
 */
public class TabuSearchImplementation {
    // 城市数量
    private int n;
    // 距离矩阵
    private double[][] distances;
    // 禁忌表（存储序列的哈希值）
    private LinkedList<Integer> tabuList;
    // 随机数生成对象
    private Random random;
    // 当前解
    private TabuSearchResult curSolution;
    // 最优解
    private TabuSearchResult bestSolution;

    // 评价函数，传入一个路径和距离矩阵，返回该路径的长度
    private static double calcPathLen(int[] path, double[][] distances) {
        double pathLen = 0d;
        for (int i = 0; i < path.length - 1; i++) {
            pathLen += distances[path[i]][path[i + 1]];
        }
        return pathLen;
    }

    public TabuSearchResult resolve(List<SiteBean> sites) {
        TabuSearchInstance tabuSearchInstance = new TabuSearchInstance();
        int length = sites.size();
        tabuSearchInstance.setN(length);
        tabuSearchInstance.setLocations(new double[tabuSearchInstance.getN()][2]);
        tabuSearchInstance.setAddress(new String[tabuSearchInstance.getN()]);
        tabuSearchInstance.setSiteIndex(new int[tabuSearchInstance.getN()]);

        for (int i = 0; i < length; i++) {
            SiteBean site = sites.get(i);
            double x = site.getLon();
            double y = site.getLat();
            String address = site.getAddress();

            tabuSearchInstance.getLocations()[i][0] = x;
            tabuSearchInstance.getLocations()[i][1] = y;
            tabuSearchInstance.getAddress()[i] = address;
            tabuSearchInstance.getSiteIndex()[i] = i;
        }

        // 初始化操作
        init(tabuSearchInstance);
        // 禁忌搜索过程
        // 迭代次数
        int epochs = 50000;
        for (int i = 1; i < epochs; i++) {
            // 在当前解进行邻域搜索，获得最佳邻域解
            Object[] localSearchResult = localSearch();
            int localBestHashValue = (int) localSearchResult[0];
            TabuSearchResult localBestSolution = (TabuSearchResult) localSearchResult[1];
            // 最佳邻域解不为null时，进行位置更新
            if (localBestSolution != null) {
                // 更新当前解
                curSolution = localBestSolution;
                // 更新禁忌表
                putInTabuList(localBestHashValue);
                // 更新全局最优解
                if (curSolution.getPathLen() < bestSolution.getPathLen()) {
                    bestSolution = curSolution;
                }
            }
        }
        return bestSolution;
    }

    // 在当前解进行邻域搜索，获得最佳邻域解
    private Object[] localSearch() {
        TabuSearchResult localBestSolution = null;
        int localBestHashValue = -1;
        // 局部搜索次数
        int localSearchCnt = 100;
        for (int j = 0; j < localSearchCnt; j++) {
            // 随机使用两个邻域算子构造新解
            int[] newPath = random.nextInt(2) == 0 ? neighborhoodOperator1(curSolution.getPath()) : neighborhoodOperator2(curSolution.getPath());
            int newHashValue = Arrays.hashCode(newPath);
            if (!isInTabuList(newHashValue)) {
                double newPathLen = calcPathLen(newPath, distances);
                if (localBestSolution == null || newPathLen < localBestSolution.getPathLen()) {
                    localBestSolution = new TabuSearchResult(newPathLen, newPath);
                    localBestHashValue = newHashValue;
                }
            }
        }
        return new Object[]{localBestHashValue, localBestSolution};
    }

    // 将解向量X的hash值加入禁忌表
    private void putInTabuList(int hashValue) {
        // 禁忌长度
        int tabuLen = 30;
        if (tabuList.size() == tabuLen) {
            tabuList.removeFirst();
        }
        tabuList.add(hashValue);
    }

    // 判断解向量X的hash值是否在禁忌表中
    private boolean isInTabuList(int hashValue) {
        for (int tabuHashValue : tabuList) {
            if (tabuHashValue == hashValue) {
                return true;
            }
        }
        return false;
    }

    // 邻域算子1：在当前解向量 X 中随机交换两个位置
    private int[] neighborhoodOperator1(int[] X) {
        int[] newX = X.clone();
        int i = random.nextInt(n);
        int j = random.nextInt(n);
        while (i == j) {
            j = random.nextInt(n);
        }
        // 采用位运算交换 i 和 j 处的两个元素
        newX[j] = newX[i] ^ newX[j];
        newX[i] = newX[i] ^ newX[j];
        newX[j] = newX[i] ^ newX[j];
        return newX;
    }

    // 邻域算子2：交换两个随机选择的索引 (i,j) 之间的所有位置
    private int[] neighborhoodOperator2(int[] X) {
        int[] newX = X.clone();
        int i = random.nextInt(n);
        int j = random.nextInt(n);
        while (i == j) {
            j = random.nextInt(n);
        }
        // 确保 i < j
        if (i > j) {
            j = i ^ j;
            i = i ^ j;
            j = i ^ j;
        }
        // 交换 (i,j) 之间的所有位置
        int sum = i + j;
        int maxI = sum / 2;
        if (sum % 2 == 0) {
            maxI--;
        }
        for (; i <= maxI; i++) {
            newX[sum - i] = newX[i] ^ newX[sum - i];
            newX[i] = newX[i] ^ newX[sum - i];
            newX[sum - i] = newX[i] ^ newX[sum - i];
        }
        return newX;
    }

    private void init(TabuSearchInstance tabuSearchInstance) {
        tabuList = new LinkedList<>();
        n = tabuSearchInstance.getN();
        // 城市坐标
        double[][] locations = tabuSearchInstance.getLocations();
        // 随机数种子
        random = new Random(2023L);
        // 计算距离矩阵
        distances = new double[n][n];
        for (int i = 0; i < distances.length; i++) {

            for (int j = i + 1; j < distances.length; j++) {
                double distance;

                distance = GEOUtil.CalcEarthTwoPointDistance(locations[i][0], locations[i][1], locations[j][0], locations[j][1]);
                distances[i][j] = distance;
                distances[j][i] = distances[i][j];
            }
        }
        // 生成初始解，为简单起见，我们考虑使用字典顺序的生成初始解决方案
        curSolution = new TabuSearchResult();
        curSolution.setPath(new int[n]);
        for (int i = 0; i < n; i++) {
            curSolution.getPath()[i] = i;
        }
        curSolution.setPathLen(calcPathLen(curSolution.getPath(), distances));
        bestSolution = curSolution.copy();
    }


    @Data
    @ToString
    private static class TabuSearchInstance {
        // 实例名
        String name;
        // 城市数量
        int n;
        // 每个城市的坐标
        double[][] locations;

        String[] address;

        int[] siteIndex;

        double[] distance;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TabuSearchResult {
        // 路程长度
        double pathLen;
        // 路径
        int[] path;

        public TabuSearchResult copy() {
            return new TabuSearchResult(pathLen, path.clone());
        }

        @Override
        public String toString() {
            return "pathLen = " + pathLen + " , path = " + Arrays.toString(path);
        }
    }
}
