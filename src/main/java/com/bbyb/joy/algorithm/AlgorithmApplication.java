package com.bbyb.joy.algorithm;

import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableSwagger2
@EnableAsync
public class AlgorithmApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(AlgorithmApplication.class);
        application.setBannerMode(Banner.Mode.OFF);
        application.run(args);
    }
}
