package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApiModel("单起点多配参数")
@Data
public class Task1ToMDBean implements Serializable {
    @Valid
    @NotNull(message = "算法参数规则不可为空！")
    private ParameterBean parameter;
    @Valid
    @NotNull(message = "起始站点不可为空！")
    private SiteBean beginSite;
    @Valid
    @NotEmpty(message = "车型信息不可为空!")
    private List<VehicleTypeBean> vehicleTypes;
    @ApiModelProperty(value = "站点矩阵信息")
    private Map<String, Map<String,Double>> matrixMap = new HashMap<>();
    @Valid
    @NotNull(message = "目的站点不可为空！")
    @NotEmpty(message = "目的站点不可为空！")
    private List<SiteBean> distinctSites;
}
