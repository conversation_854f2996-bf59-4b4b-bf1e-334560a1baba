package com.bbyb.joy.algorithm.bean;

import java.io.Serial;
import java.io.Serializable;

public class ResponseResultBean<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 8381886807295236632L;
    public int code;
    public boolean success;
    public String msg;
    public T result;

    public ResponseResultBean() {
        this.success = true;
    }

    public ResponseResultBean(boolean success, int code, String msg, T result) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.result = result;
    }

    public ResponseResultBean(int code, String msg) {
        this.success = false;
        this.code = code;
        this.msg = msg;
        this.result = null;
    }

    public ResponseResultBean(T result) {
        this.success = true;
        this.code = 0;
        this.msg = "成功！";
        this.result = result;
    }

    public ResponseResultBean(T result, String msg) {
        this.success = true;
        this.code = 0;
        this.msg = msg != null ? msg : "成功！";
        this.result = result;
    }
}