package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("处理用户自定义排线参数信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParameterBean implements Serializable {
    @ApiModelProperty(value = "每车最多配送站点数量")
    @NotNull(message = "每车最多配送站点数量不可为空！")
    @Min(message = "每车最多配送站点数量不可小于2！", value = 2L)
    private Integer maximumSiteSize;
    @ApiModelProperty(value = "目标体积装载率")
    @NotNull(message = "目标体积装载率不可为空！")
    @Min(message = "目标体积装载率不可小于0！", value = 0L)
    private Double targetLoadingRate;
    @ApiModelProperty(value = "目标重量装载率")
    @NotNull(message = "目标重量装载率不可为空！")
    @Min(message = "目标重量装载率不可小于0！", value = 0L)
    private Double targetWeightRate;
    @ApiModelProperty(value = "站点间最小间距")
    @NotNull(message = "站点间最大间距不可为空！")
    private Double betweenMaximumDistance;
    @ApiModelProperty(value = "聚类分块数量")
    @NotNull(message = "聚类分块数量不可为空！")
    private Integer clusterSize;
    @ApiModelProperty(value = "线路限制合并中心点距离")
    @NotNull(message = "线路限制合并中心点距离不可为空！")
    private Double mergeMaximumLineCenterDistance;
    @ApiModelProperty(value = "车辆平均行驶速度")
    @NotNull(message = "车辆平均行驶速度不可为空！")
    private Double vehicleAverageSpeed;
    @ApiModelProperty(value = "运力模式 0:无限运力,1:有限运力")
    @NotNull(message = "运力模式不可为空!")
    @Builder.Default
    private Integer vehiclePattern = 0;
    @ApiModelProperty(value = "是否开启禁忌算法 0:不开启,1:开启,默认:0")
    @NotNull(message = "禁忌算法是否启用不可为空!")
    private Integer enableTabu;
    @ApiModelProperty(value = "是否回仓 0:不返回,1:返回,默认:0")
    @NotNull(message = "是否回仓不可为空!")
    @Builder.Default
    private Integer enableReturn = 0;
}
