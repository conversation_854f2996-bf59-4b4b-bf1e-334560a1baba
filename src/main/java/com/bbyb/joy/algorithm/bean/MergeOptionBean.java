package com.bbyb.joy.algorithm.bean;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 合并选项类，用于比较不同的合并方案
 */
@Data
@AllArgsConstructor
public class MergeOptionBean {

    private int caseNumber;
    private List<SiteBean> mergedSites;
    private double startDistance;      // 起始点距离仓库的距离
    private double totalDistance;      // 合并后线路总距离
    private String description;

    // 兼容构造函数
    public MergeOptionBean(int caseNumber, List<SiteBean> mergedSites, double startDistance, String description) {
        this.caseNumber = caseNumber;
        this.mergedSites = mergedSites;
        this.startDistance = startDistance;
        this.totalDistance = 0.0;
        this.description = description;
    }

}
