package com.bbyb.joy.algorithm.bean;

import cn.hutool.core.util.IdUtil;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import com.bbyb.joy.algorithm.util.GEOUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@ApiModel("线路信息")
@EqualsAndHashCode(of = "code")
@Data
public class SiteLineBean implements Serializable {
    /*
     * 元数据部分
     */
    @Getter
    @ApiModelProperty("线路编码")
    private String code;

    @Getter
    @ApiModelProperty("总重量")
    private Double totalWeight;

    @Getter
    @ApiModelProperty("总体积")
    private Double totalVolume;

    @Getter
    @ApiModelProperty("总距离")
    private Double totalDistance;

    @Getter
    @ApiModelProperty("总成本")
    private Double totalCost;

    @Getter
    @ApiModelProperty("重量装载率")
    private Double loadableWeightRate;

    @Getter
    @ApiModelProperty("体积装载率")
    private Double loadableVolumeRate;

    @Getter
    @ApiModelProperty("线路中心点经纬度")
    private GEOPointBean center;

    @Getter
    @ApiModelProperty("线路中心点到起点距离，不作为标准字段")
    private Double centerToBeginSiteDistance;

    /*
     * 临时字段
     */
    @Getter
    @Setter
    @ApiModelProperty("比较距离，不作为标准字段。算法流程中比较使用")
    private Double CompareDistances;

    /*
     * 站点信息，所有其他元数据通过站点信息进行计算
     */
    @Getter
    @Setter
    @ApiModelProperty("车型信息起始点位")
    private VehicleTypeBean vehicleType;

    @Getter
    @Setter
    @ApiModelProperty("线路起始点位")
    private SiteBean beginSite;

    @Getter
    @Setter
    @ApiModelProperty("线路沿途点位")
    private List<SiteBean> sites;

    @Getter
    private SiteBean centroid;
    @Getter
    private double compactnessPenalty;   // 线路紧凑度惩罚值

    public void refreshCode() {
        this.code = IdUtil.fastSimpleUUID();
    }

    /*
     * 设置当前线路的中心点信息
     */
    private void refreshCenterPoint() {
        if (this.sites.isEmpty()) return;
        GEOPointBean centerPoint = new GEOPointBean();

        if (this.sites.size() == 1) {
            centerPoint.setLon(this.sites.get(0).getLon());
            centerPoint.setLat(this.sites.get(0).getLat());
        } else {
            // 经纬度分别平均计算就是中心点位经纬度
            double lon = this.sites.stream().mapToDouble(SiteBean::getLon).average().orElse(0.0);
            double lat = this.sites.stream().mapToDouble(SiteBean::getLat).average().orElse(0.0);
            centerPoint.setLat(lat);
            centerPoint.setLon(lon);
        }

        this.center = centerPoint;
    }

    /*
     * 设置当前线路总重量
     */
    private void refreshTotalWeight() {
        this.totalWeight = this.sites.stream().mapToDouble(SiteBean::getWeight).sum();
    }

    /*
     * 设置当前线路总体积
     */
    private void refreshTotalVolume() {
        this.totalVolume = this.sites.stream().mapToDouble(SiteBean::getVolume).sum();
    }

    /**
     * 计算线路总距离
     * 根据enableReturn配置决定是否包含返仓距离
     * 计算方式：
     * 1. 从仓库到第一个站点的距离
     * 2. 所有站点之间的距离之和
     * 3. 如果enableReturn=1，则包含最后一个站点返回仓库的距离
     */
    private void refreshTotalDistance() {
        // 如果没有站点，总距离为0
        if (this.sites == null || this.sites.isEmpty()) {
            this.totalDistance = 0.0;
            return;
        }

        double totalDistance = 0.0;
        int siteCount = this.sites.size();

        // 1. 从仓库到第一个站点的距离
        SiteBean firstSite = this.sites.get(0);
        totalDistance += MatrixContext.getMatrixMap(this.beginSite.getCode())
                .getOrDefault(firstSite.getCode(), 0.0);

        // 2. 所有站点之间的距离之和
        for (int i = 0; i < siteCount - 1; i++) {
            SiteBean currentSite = this.sites.get(i);
            SiteBean nextSite = this.sites.get(i + 1);

            totalDistance += MatrixContext.getMatrixMap(currentSite.getCode())
                    .getOrDefault(nextSite.getCode(), 0.0);
        }

        // 3. 根据enableReturn配置决定是否包含返仓距离
        ParameterBean parameter = TaskContext.getParameter();
        if (parameter != null && parameter.getEnableReturn() != null && parameter.getEnableReturn() == 1) {
            SiteBean lastSite = this.beginSite;
            totalDistance += MatrixContext.getMatrixMap(lastSite.getCode())
                    .getOrDefault(this.beginSite.getCode(), 0.0);
        }

        this.totalDistance = totalDistance;
    }

    /*
     * 设置当前线路中心点到起点距离
     */
    private void refreshCenterToBeginSiteDistance() {
        SiteBean beginSite = this.beginSite;
        GEOPointBean centerPoint = this.center;

        this.centerToBeginSiteDistance = GEOUtil.CalcEarthTwoPointDistance(beginSite.getLon(), beginSite.getLat(), centerPoint.getLon(), centerPoint.getLat());
    }

    private void calculateCentroidAndCompactness() {
        if (sites == null || sites.isEmpty()) {
            this.centroid = null; // 或者设置为仓库位置
            this.compactnessPenalty = 0.0;
            return;
        }

        double totalLat = 0;
        double totalLon = 0;
        for (SiteBean site : sites) {
            totalLat += site.getLat();
            totalLon += site.getLon();
        }

        double avgLat = totalLat / sites.size();
        double avgLon = totalLon / sites.size();
        this.centroid = new SiteBean("centroid", avgLat, avgLon); // 假设SiteBean有这样的构造函数

        double sumOfSquaredDistances = 0;
        for (SiteBean site : sites) {
            // 注意：这里的 getDistance 需要能处理点到质心的距离计算
            // 最好使用欧几里得距离的平方，以避免开方，提高效率
            // d^2 = (lat1-lat2)^2 + (lon1-lon2)^2
            sumOfSquaredDistances += GEOUtil.getEuclideanDistanceSquared(site, this.centroid);
        }
        this.compactnessPenalty = sumOfSquaredDistances;
    }


    /**
     * 线路装载率
     * 注意：该方法要放到车型和站点信息都确定之后再调用
     */
    public void refereshLoadingRate() {
        this.loadableWeightRate = this.totalWeight != null && this.totalWeight > 0 ?
                this.totalWeight / this.vehicleType.getLoadableWeight() : 0.0;
        this.loadableVolumeRate = this.totalVolume != null && this.totalVolume > 0 ?
                this.totalVolume / this.vehicleType.getLoadableVolume() : 0.0;
    }

    /**
     * 线路总成本
     * 注意：该方法要放到车型和站点信息都确定之后再调用
     */
    public void refereshTotalCost() {

        Double fixedCost = this.vehicleType.getFixedCost();
        Double perDistanceUnit = this.vehicleType.getPerDistanceUnit();

        this.totalCost = (fixedCost != null ? fixedCost : 0.0) +
                (perDistanceUnit != null ? perDistanceUnit * this.totalDistance : 0.0);
    }



    /**
     * 更新线路中各站点的距离信息
     * 计算每个站点到前一个站点的距离，并设置到站点的totalDistance字段
     * 第一个站点的前一个站点为起始仓库(beginSite)
     */
    private void refreshSitesTotalDistance() {
        if (this.sites == null || this.sites.isEmpty()) return;
        
        // 累计距离，用于跟踪路线总长度
        double cumulativeDistance = 0.0;
        
        // 处理第一个站点，从起始仓库到第一个站点
        SiteBean firstSite = this.sites.get(0);
        double distanceFromDepot = MatrixContext.getMatrixMap(this.beginSite.getCode())
                .getOrDefault(firstSite.getCode(), 0.0);
        firstSite.setTotalDistance(distanceFromDepot);
        cumulativeDistance += distanceFromDepot;
        
        // 处理剩余站点，计算每个站点与前一个站点之间的距离
        for (int i = 1; i < this.sites.size(); i++) {
            SiteBean currentSite = this.sites.get(i);
            SiteBean previousSite = this.sites.get(i - 1);
            
            double distance = MatrixContext.getMatrixMap(previousSite.getCode())
                    .getOrDefault(currentSite.getCode(), 0.0);
            currentSite.setTotalDistance(distance);
            cumulativeDistance += distance;
        }
    }
    
    public void refreshMetaData() {
        this.refreshTotalWeight();
        this.refreshTotalVolume();

        if (this.sites.isEmpty()) return;
        this.refreshCenterPoint();
        this.refreshCenterToBeginSiteDistance();
        this.refreshSitesTotalDistance(); // 先更新各站点的距离
        this.refreshTotalDistance(); // 再计算总距离
        this.calculateCentroidAndCompactness();
        this.refereshLoadingRate();
        this.refereshTotalCost();
    }
}
