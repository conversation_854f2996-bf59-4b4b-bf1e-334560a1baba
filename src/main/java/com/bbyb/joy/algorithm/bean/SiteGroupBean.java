package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel("区域划分分组结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SiteGroupBean implements Serializable {
    @ApiModelProperty("区域最大纬度")
    private Double maxLat;
    @ApiModelProperty("区域最小纬度")
    private Double minLat;
    @ApiModelProperty("区域最大经度")
    private Double maxLon;
    @ApiModelProperty("区域最小经度")
    private Double minLon;
    @ApiModelProperty("区域平均纬度（区域中心点纬度）")
    private Double avgLat;
    @ApiModelProperty("区域平均经度（区域中心点经度）")
    private Double avgLon;
    @ApiModelProperty("区域分组标识")
    private String group;
    @ApiModelProperty("该区域包含的订单列表")
    private List<SiteBean> sites;
}
