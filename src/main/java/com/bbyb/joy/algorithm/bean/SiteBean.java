package com.bbyb.joy.algorithm.bean;

import com.bbyb.joy.algorithm.context.MatrixContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("站点信息")
@Data
@EqualsAndHashCode(of = "code")
public class SiteBean implements Serializable {
    @ApiModelProperty("编码")
    @NotNull(message = "站点编码不可为空！")
    private String code;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("重量")
    private Double weight;
    @ApiModelProperty("体积")
    private Double volume;
    @ApiModelProperty("经度")
    @NotNull(message = "站点经度不可为空！")
    private Double lon;
    @NotNull(message = "站点纬度不可为空！")
    @ApiModelProperty("纬度")
    private Double lat;
    @ApiModelProperty(value = "开放时间")
    private String openTime;
    @ApiModelProperty(value = "关闭时间")
    private String closeTime;
    @ApiModelProperty(value = "到起点距离")
    private Double distance;
    @ApiModelProperty("当前站点到上一站点的距离")
    private Double totalDistance;

    public SiteBean(String code, Double lon, Double lat) {
        this.code = code;
        this.lon = lon;
        this.lat = lat;
    }
}
