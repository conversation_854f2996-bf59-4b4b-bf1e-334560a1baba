package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel("车辆信息")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VehicleTypeBean implements Serializable {
    @Getter
    @Setter
    @ApiModelProperty(value = "编码")
    @NotNull(message = "车型编码不可为空!")
    private String code;

    @Getter
    @Setter
    @NotNull(message = "车型核载重量不可为空!")
    @Min(value = 0L, message = "车型核载重量不可小于0！")
    @ApiModelProperty(value = "核载重量")
    private Double weight;

    @Getter
    @Setter
    @NotNull(message = "车型核载体积不可为空!")
    @Min(value = 0L, message = "车型核载体积不可小于0！")
    @ApiModelProperty(value = "核载体积")
    private Double volume;

    @Getter
    @ApiModelProperty(value = "配载重量限制，算法规则配置")
    private Double loadableWeight;

    @Getter
    @ApiModelProperty(value = "配载重量装载率，算法规则配置")
    private Double loadableWeightRate;

    @Getter
    @ApiModelProperty(value = "配载体积限制，算法规则配置")
    private Double loadableVolume;

    @Getter
    @ApiModelProperty(value = "配载体积装载率，算法规则配置")
    private Double loadableVolumeRate;

    @Getter
    @Setter
    @ApiModelProperty(value = "固定成本, 运力池车型配置")
    private Double fixedCost;

    @Getter
    @Setter
    @ApiModelProperty(value = "每公里行驶成本, 运力池车型配置")
    private Double perDistanceUnit;

    @Getter
    @Setter
    @ApiModelProperty(value = "车辆信息")
    private List<VehicleInfoBean> vehicles;

    public void setLoadableWeightRate(double loadableWeightRate) {
        this.loadableWeightRate = loadableWeightRate;

        if (this.volume > 0) {
            this.loadableWeight = loadableWeightRate * this.weight;
        }
    }

    public void setLoadableVolumeRate(double loadableVolumeRate) {
        this.loadableVolumeRate = loadableVolumeRate;

        if (this.volume > 0) {
            this.loadableVolume = loadableVolumeRate * this.volume;
        }
    }


    /**
     * 计算使用该车型的总成本
     * @param distance 行驶距离
     * @return 总成本
     */
    public Double calculateTotalCost(Double distance) {
        if (distance == null) distance = 0.0;

        double fixed = this.fixedCost != null ? this.fixedCost : 0.0;
        double variable = this.perDistanceUnit != null ? this.perDistanceUnit * distance : 0.0;

        return fixed + variable;
    }

    /**
     * 检查是否能承载指定重量和体积
     */
    public boolean canCarry(double weight, double volume) {
        return (this.loadableWeight != null && this.loadableWeight >= weight) &&
                (this.loadableVolume != null && this.loadableVolume >= volume);
    }

    /**
     * 计算装载率
     */
    public double getUtilizationRate(double actualWeight, double actualVolume) {
        double weightRate = this.loadableWeight != null && this.loadableWeight > 0 ?
                actualWeight / this.loadableWeight : 0.0;
        double volumeRate = this.loadableVolume != null && this.loadableVolume > 0 ?
                actualVolume / this.loadableVolume : 0.0;

        return Math.max(weightRate, volumeRate);
    }



}
