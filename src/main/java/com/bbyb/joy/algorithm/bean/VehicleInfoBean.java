package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel("车辆信息-暂不使用")
@NoArgsConstructor
@AllArgsConstructor
public class VehicleInfoBean implements Serializable {

    @Getter
    @ApiModelProperty(value = "车辆编码")
    @NotNull(message = "车辆编码不可为空!")
    private String code;

    @Getter
    @ApiModelProperty(value = "车型信息")
    @NotNull(message = "车型不可为空!")
    private VehicleTypeBean vehicleTypeBean;




}
