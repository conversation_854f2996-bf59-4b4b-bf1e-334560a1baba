package com.bbyb.joy.algorithm.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交叉片段信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrossingSegmentBean {
    private int segment1Start; // 线路1交叉片段起始索引
    private int segment1End; // 线路1交叉片段结束索引
    private int segment2Start; // 线路2交叉片段起始索引
    private int segment2End; // 线路2交叉片段结束索引
}
