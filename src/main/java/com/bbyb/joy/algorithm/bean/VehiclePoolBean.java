package com.bbyb.joy.algorithm.bean;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@ApiModel("运力池-暂不使用")
@NoArgsConstructor
@AllArgsConstructor
public class VehiclePoolBean implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Getter
    @Setter
    @NotEmpty(message = "运力信息不可为空!")
    private List<VehicleInfoBean> vehicleInfos;


}
