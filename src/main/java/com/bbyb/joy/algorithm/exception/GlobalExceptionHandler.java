package com.bbyb.joy.algorithm.exception;

import com.bbyb.joy.algorithm.bean.ResponseResultBean;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResultBean<String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        StringBuffer sb = new StringBuffer();

        ex.getBindingResult().getFieldErrors().forEach(e -> sb.append(e.getDefaultMessage()));
        return new ResponseResultBean<>(false, 400, "请求参数无效！", sb.toString());
    }

    @ExceptionHandler(Exception.class)
    public ResponseResultBean<String> handleAllExceptions(Exception ex) {
        return new ResponseResultBean<>(false, 400, "系统异常！", ex.getMessage());
    }
}
