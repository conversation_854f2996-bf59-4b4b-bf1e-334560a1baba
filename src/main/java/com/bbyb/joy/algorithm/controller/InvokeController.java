package com.bbyb.joy.algorithm.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.bbyb.joy.algorithm.bean.*;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import com.bbyb.joy.algorithm.implementation.KMeansPlusPlusImplementation;
import com.bbyb.joy.algorithm.solution.ClarkeWright.ClarkeWrightOptimizer;
import com.bbyb.joy.algorithm.solution.TabuSearch.TabuSearchOptimizer;
import com.bbyb.joy.algorithm.solution.TabuSearch.TabuSearchUtil;
import com.bbyb.joy.algorithm.solution.finalSearch.FinalRouteOptimizer;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/invoke")
@CrossOrigin
@Log4j2
@Api(tags = "外部调用控制器入口")
public class InvokeController {

    @PostMapping("/Execute1ToMDTask")
    @ApiOperation("同步执行单仓配送多门店任务")
    public ResponseResultBean<Task1ToMDResponseBean> SyncExecute1ToMDTask(@Valid @RequestBody Task1ToMDBean task1ToMDBean) {
        try {
            log.debug("单仓配送多门店排线任务开始执行！");

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("排线算法");
            stopWatch.stop();

            stopWatch.start("数据初始化");
            String s = ResourceUtil.readUtf8Str("MockData2.json");
            task1ToMDBean = JSONUtil.toBean(JSONUtil.toJsonStr(s), Task1ToMDBean.class);

            task1ToMDBean.getParameter().setEnableReturn(0);

            // 0. 初始化任务标识
            TaskContext.initTaskCode();
            log.debug("生成任务标识完成，任务标识：{}", TaskContext.getTaskCode());

            // 1. 算法规则信息
            ParameterBean parameter = task1ToMDBean.getParameter();
            TaskContext.setParameter(parameter);
            log.debug("获取算法规则信息完成，算法规则：{}", JSON.toJSONString(parameter));

            // 2. 起始站点信息
            SiteBean beginSite = task1ToMDBean.getBeginSite();
            TaskContext.setBeginSite(beginSite);
            log.debug("获取起始站点信息完成，起始站点编码：{}", beginSite.getCode());

            // 3. 目的站点列表
            List<SiteBean> destinationSites = task1ToMDBean.getDistinctSites();
            TaskContext.setFreeDestinationSites(destinationSites);
            log.debug("获取目的站点信息完成，目的站点数量：{}", destinationSites.size());

            // 4. 运力信息
            List<VehicleTypeBean> vehicleTypes = task1ToMDBean.getVehicleTypes();
            double loadableWeightRate = parameter.getTargetWeightRate();
            double loadableVolumeRate = parameter.getTargetLoadingRate();
            log.debug("算法规则配置重量装载率为：{}", loadableWeightRate);
            log.debug("算法规则配置体积装载率为：{}", loadableVolumeRate);

            for (VehicleTypeBean vehicleType : vehicleTypes) {
                vehicleType.setLoadableWeightRate(loadableWeightRate);
                vehicleType.setLoadableVolumeRate(loadableVolumeRate);
            }
            TaskContext.setVehicleTypes(vehicleTypes);
            log.debug("获取车型信息完成，车型信息：{}", JSON.toJSONString(vehicleTypes));

            // 5. 起始站点和目的站点进行合并计算矩阵信息
            List<SiteBean> matrixSites = new ArrayList<>();
            matrixSites.add(beginSite);
            matrixSites.addAll(destinationSites);
            MatrixContext.refreshMatrixMap(matrixSites, task1ToMDBean.getMatrixMap());

            /*
             * 1. 使用 K-Means++ 聚类算法进行分区。
             * 2. 对每个分区进行排线。
             */
            List<SiteGroupBean> siteGroupByKMeans = Lists.newArrayList();
            int totalClusterNum = 0;
            int KMeansAreaCount = parameter.getClusterSize();
            List<List<SiteBean>> splitSites = new ArrayList<>();

            if (destinationSites.size() <= KMeansAreaCount) {
                splitSites.add(destinationSites);
            } else {
                log.debug("开始使用 K-Means++ 算法进行点位分区。");
                KMeansPlusPlusImplementation cluster = new KMeansPlusPlusImplementation(KMeansAreaCount);
                splitSites = cluster.resolve(destinationSites);
                log.debug("K-Means++ 算法点位分区区块数量为：{}", KMeansAreaCount);
            }

            int clusterAreaIndex = 1;
            for (List<SiteBean> splitSite : splitSites) {
                totalClusterNum += splitSite.size();
                List<String> partitionedSiteCodes = splitSite.stream().map(SiteBean::getCode).toList();
                //区域平均经度
                double avgLatitude = splitSite.stream().mapToDouble(SiteBean::getLat).average().orElse(0.0); // 如果流为空则返回 0.0
                //区域平均纬度
                double avgLongitude = splitSite.stream().mapToDouble(SiteBean::getLon).average().orElse(0.0); // 如果流为空则返回 0.0
                //区域重量装载率
                double totalWeight = splitSite.stream().mapToDouble(SiteBean::getWeight).sum();
                double weightRate = totalWeight / 30;

                //区域体积装载率
                double totalVolume = splitSite.stream().mapToDouble(SiteBean::getVolume).sum();
                double volumeRate = totalVolume / 12 * 100;
                log.debug("当前分区索引【{}】点位数量【{}】平均经度【{}】平均纬度【{}】重量装载率【{}】体积装载率【{}】", clusterAreaIndex, splitSite.size(), avgLatitude, avgLongitude, weightRate, volumeRate);

                log.debug("开始构建点位分区信息。");
                siteGroupByKMeans.add(SiteGroupBean.builder().group(String.valueOf(clusterAreaIndex)).avgLat(avgLatitude).avgLon(avgLongitude).sites(splitSite).build());

                /*
                 * 把已经分区的点位从上下文里面去掉
                 */
                TaskContext.setFreeDestinationSites(TaskContext.getFreeDestinationSites().stream().filter(n -> !partitionedSiteCodes.contains(n.getCode())).toList());
                log.debug("上下文中还剩【{}】点位没有进行分区。", TaskContext.getFreeDestinationSites().size());
                log.debug("结束构建点位分区信息。");
                clusterAreaIndex++;
            }
            log.debug("结束构建分区信息，共【{}】个点位进行分区，生成 【{}】个分区信息。", totalClusterNum, siteGroupByKMeans.size());
            stopWatch.stop();

            stopWatch.start("节约处理");
            log.debug("开始按分区结果循环调用生成排线基础线路。");
            int groupIndex = 1;
            List<SiteLineBean> siteLines = new ArrayList<>();
            String s1 = ResourceUtil.readUtf8Str("MockData.json");
            siteLines = JSONUtil.toList(s1, SiteLineBean.class);
            for (SiteGroupBean siteGroup : siteGroupByKMeans) {
                log.info("编号【{}】区域调用订单数:{}", groupIndex, siteGroup.getSites().size());
                ClarkeWrightOptimizer cwOptimizer = new ClarkeWrightOptimizer();
                siteLines.addAll(cwOptimizer.resolver(siteGroup.getSites()));
                groupIndex++;
            }
            log.debug("节约处理结束");
            stopWatch.stop();

            if(parameter.getEnableTabu().equals(1)){
                stopWatch.start("禁忌算法开始");

                // 根据总站点数动态计算禁忌搜索参数
                int totalSites = siteLines.stream().mapToInt(n -> n.getSites().size()).sum();
                int maxIterations = TabuSearchUtil.calculateMaxIterations(totalSites);
                int tabuTenure = TabuSearchUtil.calculateTabuTenure(totalSites);

                log.debug("禁忌搜索参数配置 - 总站点数：{}，最大迭代次数：{}，禁忌期长度：{}", totalSites, maxIterations, tabuTenure);

                TabuSearchOptimizer optimizer = new TabuSearchOptimizer(1000, 50);
                siteLines = optimizer.resolver(siteLines);
                log.debug("使用禁忌算法优化线路成功。线路数量为：{}，站点数量为：{}", siteLines.size(), siteLines.stream().mapToInt(n -> n.getSites().size()).sum());
                stopWatch.stop();
            }

            log.debug("开始最终线路优化器");
            log.info("最终线路优化器优化数据:{}",JSONUtil.toJsonStr(siteLines));
             siteLines = FinalRouteOptimizer.optimizeAllRoutes(siteLines);
            log.debug("使用增强路顺优化线路成功。线路数量为：{}，站点数量为：{}", siteLines.size(), siteLines.stream().mapToInt(n -> n.getSites().size()).sum());

            log.debug("设置线路到缓存中！");
            TaskContext.setCompleteSiteLines(siteLines);

            log.debug("结束按策略执行基础线路优化。");

            Task1ToMDResponseBean responseBean = new Task1ToMDResponseBean();
            responseBean.setSiteLines(TaskContext.getCompleteSiteLines());

            log.debug("单仓配送多门店排线任务流程结束！");

            log.info("排线结果:{}",stopWatch.prettyPrint(TimeUnit.SECONDS));
            return new ResponseResultBean<>(responseBean);
        } finally {
            // 清理任务的上下文信息
            TaskContext.clear();
        }
    }
}

