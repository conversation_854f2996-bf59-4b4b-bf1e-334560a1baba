package com.bbyb.joy.algorithm.util;

import cn.hutool.core.bean.BeanUtil;
import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.bean.VehicleTypeBean;
import com.bbyb.joy.algorithm.context.TaskContext;

import java.awt.geom.Line2D;
import java.util.ArrayList;
import java.util.List;

/**
 * 线路相关工具类
 */
public class SiteLinesUtil {
    /**
     * 判断线路有没有和已经排出来的线路相交
     *
     * @param siteLine 需要判断的希腊路
     * @return true 不相交，false 相交
     */
    public static Boolean isLineNoCrossWithCompleteSiteLines(SiteLineBean siteLine, List<SiteLineBean> completeSiteLines) {
        List<SiteBean> sites = siteLine.getSites();
        if (sites == null || sites.isEmpty() || sites.size() < 2) return true;

        List<Double[]> splitPoint = new ArrayList<>();
        for (SiteLineBean line : completeSiteLines) {
            int siteIndex = 0;
            while (siteIndex < line.getSites().size() - 1) {
                splitPoint.add(new Double[]{line.getSites().get(siteIndex).getLon(), line.getSites().get(siteIndex).getLat(), line.getSites().get(siteIndex + 1).getLon(), line.getSites().get(siteIndex + 1).getLat(), sites.get(0).getLon(), sites.get(0).getLat(), sites.get(sites.size() - 1).getLon(), sites.get(sites.size() - 1).getLat()});
                siteIndex++;
            }
        }
        return splitPoint.stream().noneMatch(s -> Line2D.linesIntersect(s[0], s[1], s[2], s[3], s[4], s[5], s[6], s[7]));
    }

    /**
     * 判断线路是否超载
     *
     * @param siteLine 线路点位信息
     * @return true 不超载，false 超载
     */
    public static Boolean isLineNoOverLoad(VehicleTypeBean vehicleType, SiteLineBean siteLine) {
        List<SiteBean> sites = siteLine.getSites();

        if (sites == null || sites.isEmpty()) return true;

        double totalVolume = sites.stream().mapToDouble(SiteBean::getVolume).sum();
        double totalWeight = sites.stream().mapToDouble(SiteBean::getWeight).sum();
        return !(totalVolume >= vehicleType.getLoadableVolume()) && !(totalWeight >= vehicleType.getLoadableWeight());
    }

    /**
     * 合并两条线路返回新线路信息
     *
     * @param primaryLine      主要合并的线路
     * @param pendingMergeLine 需要合并过来的线路
     * @return 新线路信息
     */
    public static SiteLineBean getLineByMergeTwoLine(SiteLineBean primaryLine, SiteLineBean pendingMergeLine) {
        SiteLineBean mergeLine = BeanUtil.copyProperties(primaryLine, SiteLineBean.class);

        List<SiteBean> primarySites = primaryLine.getSites();
        primarySites.addAll(pendingMergeLine.getSites());

        mergeLine.setSites(primarySites);
        return mergeLine;
    }

    /**
     * 判断线路是否满足时间窗约束
     *
     * @param siteLine 线路点位信息
     * @return true 满足时间窗，false 不满足
     */
    public static Boolean isLineTimeWindowFeasible(SiteLineBean siteLine) {
        return TimeWindowUtil.isRouteTimeWindowFeasible(siteLine);
    }

    /**
     * 判断线路是否满足所有约束条件（载重、时间窗等）
     *
     * @param siteLine 线路点位信息
     * @return true 满足所有约束，false 不满足
     */
    public static Boolean isLineFeasible(VehicleTypeBean vehicleType,SiteLineBean siteLine) {
        return isLineNoOverLoad(vehicleType, siteLine) && isLineTimeWindowFeasible(siteLine);
    }
}
