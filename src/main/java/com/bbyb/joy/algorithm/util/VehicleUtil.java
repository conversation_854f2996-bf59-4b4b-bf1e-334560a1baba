package com.bbyb.joy.algorithm.util;

import cn.hutool.core.collection.CollUtil;
import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.bean.VehiclePoolBean;
import com.bbyb.joy.algorithm.bean.VehicleTypeBean;
import com.bbyb.joy.algorithm.context.TaskContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class VehicleUtil {


    /**
     * FSMVRP车型选择 - 基于总成本最优
     * 考虑固定成本 + 运营成本的综合最优解
     */
    public static VehicleTypeBean optimalVehicleTypeForFSMVRP(SiteLineBean siteLine) {
        List<VehicleTypeBean> vehicleTypes = TaskContext.getVehicleTypes();
        if (CollUtil.isEmpty(vehicleTypes)) {
            return null;
        }

        List<SiteBean> sites = siteLine.getSites();
        if (sites == null || sites.isEmpty()) {
            return vehicleTypes.get(0);
        }

        double totalWeight = sites.stream().mapToDouble(SiteBean::getWeight).sum();
        double totalVolume = sites.stream().mapToDouble(SiteBean::getVolume).sum();
        double totalDistance = siteLine.getTotalDistance()!=null?siteLine.getTotalDistance():0.0;

        VehicleTypeBean optimalVehicle = null;
        double minTotalCost = Double.MAX_VALUE;

        for (VehicleTypeBean vehicleType : vehicleTypes) {
            // 检查载重约束
            if (vehicleType.getLoadableWeight() < totalWeight || vehicleType.getLoadableVolume() < totalVolume) {
                continue;
            }

            // 计算总成本 = 固定成本 + 距离成本
            double totalCost = calculateVehicleTotalCost(vehicleType, totalDistance);

            if (totalCost < minTotalCost) {
                minTotalCost = totalCost;
                optimalVehicle = vehicleType;
            }
        }

        return optimalVehicle;
    }

    /**
     * 计算车型总成本
     */
    private static double calculateVehicleTotalCost(VehicleTypeBean vehicleType, double distance) {
        double fixedCost = vehicleType.getFixedCost() != null ? vehicleType.getFixedCost() : 0.0;
        double distanceCost = vehicleType.getPerDistanceUnit() != null ?
                vehicleType.getPerDistanceUnit() * distance : 0.0;
        return fixedCost + distanceCost;
    }

    /**
     * 合并线路时的车型选择
     */
    public static VehicleTypeBean optimalVehicleTypeForMergedLinesFSMVRP(SiteLineBean siteLineA, SiteLineBean siteLineB) {
        List<VehicleTypeBean> vehicleTypes = TaskContext.getVehicleTypes();
        if (CollUtil.isEmpty(vehicleTypes)) {
            return null;
        }

        // 计算合并后的载重和体积
        double totalWeight = 0.0;
        double totalVolume = 0.0;
        double totalDistance = 0.0;

        if (siteLineA != null && siteLineA.getSites() != null) {
            totalWeight += siteLineA.getSites().stream().mapToDouble(SiteBean::getWeight).sum();
            totalVolume += siteLineA.getSites().stream().mapToDouble(SiteBean::getVolume).sum();
        }
        if (siteLineB != null && siteLineB.getSites() != null) {
            totalWeight += siteLineB.getSites().stream().mapToDouble(SiteBean::getWeight).sum();
            totalVolume += siteLineB.getSites().stream().mapToDouble(SiteBean::getVolume).sum();
        }

        // 估算合并后距离（简化计算）
        totalDistance = (siteLineA != null ? siteLineA.getTotalDistance() : 0.0) +
                (siteLineB != null ? siteLineB.getTotalDistance() : 0.0);

        VehicleTypeBean optimalVehicle = null;
        double minTotalCost = Double.MAX_VALUE;

        for (VehicleTypeBean vehicleType : vehicleTypes) {
            // 检查载重约束
            if (vehicleType.getLoadableWeight() < totalWeight || vehicleType.getLoadableVolume() < totalVolume) {
                continue;
            }

            double totalCost = calculateVehicleTotalCost(vehicleType, totalDistance);

            if (totalCost < minTotalCost) {
                minTotalCost = totalCost;
                optimalVehicle = vehicleType;
            }
        }

        return optimalVehicle;
    }



    /**
     * 根据多个站点集合获取最优车型（考虑成本效益）
     * 在满足装载要求的前提下，优先选择载重量最小的车型以降低成本
     *
     * @param sites 站点集合
     * @return 最优车型，如果没有合适的车型则返回null
     */
    public static VehicleTypeBean optimalVehicleTypeWithCostForSites(List<SiteBean> sites) {
        List<VehicleTypeBean> vehicleTypes = TaskContext.getVehicleTypes();
        if (CollUtil.isEmpty(vehicleTypes)) {
            return null;
        }

        if (sites == null || sites.isEmpty()) {
            return vehicleTypes.get(0); // 返回第一个车型作为默认值
        }

        // 计算所有站点的总重量和总体积
        double totalWeight = sites.stream().mapToDouble(SiteBean::getWeight).sum();
        double totalVolume = sites.stream().mapToDouble(SiteBean::getVolume).sum();

        VehicleTypeBean optimalVehicle = null;
        double minCapacity = Double.MAX_VALUE;

        // 遍历所有车型，找到能承载且载重量最小的车型
        for (VehicleTypeBean vehicleType : vehicleTypes) {
            // 检查车型是否能承载所有站点
            if (vehicleType.getLoadableWeight() >= totalWeight && vehicleType.getLoadableVolume() >= totalVolume) {
                // 计算车型的综合载重能力（重量和体积的加权平均）
                double capacity = vehicleType.getLoadableWeight() + vehicleType.getLoadableVolume();

                // 选择载重能力最小的车型（成本最低）
                if (capacity < minCapacity) {
                    minCapacity = capacity;
                    optimalVehicle = vehicleType;
                }
            }
        }

        return optimalVehicle;
    }




}
