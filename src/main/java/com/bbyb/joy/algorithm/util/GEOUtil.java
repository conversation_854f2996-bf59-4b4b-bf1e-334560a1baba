package com.bbyb.joy.algorithm.util;

import com.bbyb.joy.algorithm.bean.SiteBean;

import java.text.DecimalFormat;

public class GEOUtil {
    private static final double EARTH_RADIUS = 6378.137; //地球赤道半径(单位m)

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 根据经纬度计算地球表面两点间的距离(粗略计算：直线距离 * 系数)
     * @return 公里（KM）
     */
    public static double CalcEarthTwoPointDistance(double lng1, double lat1, double lng2, double lat2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = s * 1.2;

        DecimalFormat df = new DecimalFormat("#0.000000"); //保留6位小数
        return Double.parseDouble(df.format(s));
    }

    public static double getEuclideanDistanceSquared(SiteBean s1, SiteBean s2) {
        double latDiff = s1.getLat() - s2.getLat();
        double lonDiff = s1.getLon() - s2.getLon();
        return latDiff * latDiff + lonDiff * lonDiff;
    }
}
