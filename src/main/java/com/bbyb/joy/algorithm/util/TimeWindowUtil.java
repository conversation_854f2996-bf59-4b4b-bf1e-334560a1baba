package com.bbyb.joy.algorithm.util;

import com.bbyb.joy.algorithm.bean.SiteBean;
import com.bbyb.joy.algorithm.bean.SiteLineBean;
import com.bbyb.joy.algorithm.context.MatrixContext;
import com.bbyb.joy.algorithm.context.TaskContext;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class TimeWindowUtil {
    /*
     * 站点固定停留时间 30 分钟
     */
    private static final long SERVICE_TIME_MINUTES = 30L;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    public static boolean isRouteTimeWindowFeasible(SiteLineBean route) {
        if (route.getSites() == null || route.getSites().isEmpty()) {
            return true;
        }

        try {
            LocalDateTime currentDate = LocalDateTime.parse("1999-01-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String firstSiteEnd = route.getSites().get(0).getOpenTime();
            if (!firstSiteEnd.isEmpty()) {
                currentDate.plusHours(LocalTime.parse(firstSiteEnd, TIME_FORMATTER).getHour());
                currentDate.plusMinutes(LocalTime.parse(firstSiteEnd, TIME_FORMATTER).getMinute());
            }

            // 检查后续站点
            for (int i = 1; i < route.getSites().size(); i++) {
                currentDate = currentDate.plusMinutes(SERVICE_TIME_MINUTES);
                SiteBean prevSite = route.getSites().get(i - 1);
                SiteBean currentSite = route.getSites().get(i);

                double distanceBetween = MatrixContext.getMatrixMap(currentSite.getCode()).get(prevSite.getCode());

                long travelTime = calculateTravelTimeMinutes(distanceBetween);
                currentDate = currentDate.plusMinutes(travelTime);

                // 检查当前站点的时间窗
                if (!isWithinTimeWindow(currentDate, currentSite)) {
                    return false;
                }
                currentDate = getLeaveTime(currentDate, currentSite);
            }

            return true;

        } catch (Exception e) {
            log.error("检查时间窗约束时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 计算两点之间的行驶时间（分钟）
     */
    public static long calculateTravelTimeMinutes(double distanceKm) {

        if (distanceKm <= 0) {
            return 0L;
        }
        return Math.round((distanceKm / TaskContext.getParameter().getVehicleAverageSpeed()) * 60);
    }

    /**
     * 检查给定时间是否在站点的时间窗内
     */
    public static boolean isWithinTimeWindow(LocalDateTime arrivalDate, SiteBean site) {
        if (site.getOpenTime() == null || site.getCloseTime() == null) {
            return true; // 没有时间窗限制
        }

        try {
            LocalTime openTime = LocalTime.parse(site.getOpenTime(), TIME_FORMATTER);
            LocalTime closeTime = LocalTime.parse(site.getCloseTime(), TIME_FORMATTER);

            LocalDateTime closeDateTime = LocalDateTime.of(LocalDate.from(arrivalDate), closeTime);

            if (closeTime.isAfter(openTime)) {
                closeDateTime = closeDateTime.plusDays(1);
            }

            return arrivalDate.isBefore(closeDateTime);
        } catch (Exception e) {
            log.warn("解析时间窗格式失败: openTime={}, closeTime={}", site.getOpenTime(), site.getCloseTime());
            return true; // 格式错误时允许通过
        }
    }

    public static LocalDateTime getLeaveTime(LocalDateTime arrivalTime, SiteBean site) {
        LocalDateTime leaveTime = LocalDateTime.from(arrivalTime);

        LocalDateTime openDateTime = LocalDateTime.of(LocalDate.from(arrivalTime), LocalTime.parse(site.getOpenTime(), TIME_FORMATTER));
        if (arrivalTime.isBefore(openDateTime)) {
            return openDateTime.plusMinutes(SERVICE_TIME_MINUTES);
        } else {
            return leaveTime.minusMinutes(SERVICE_TIME_MINUTES);
        }
    }

    /**
     * 检查合并两条线路是否满足时间窗约束
     */
    public static boolean isMergeTimeWindowFeasible(SiteLineBean routeA, SiteLineBean routeB, SiteBean connectionPoint) {
        // 创建合并后的临时线路进行测试
        SiteLineBean mergedRoute = new SiteLineBean();
        mergedRoute.setBeginSite(routeA.getBeginSite());
        mergedRoute.setVehicleType(routeA.getVehicleType());

        List<SiteBean> mergedSites = new ArrayList<>(routeA.getSites());
        mergedSites.addAll(routeB.getSites());
        mergedRoute.setSites(mergedSites);

        return isRouteTimeWindowFeasible(mergedRoute);
    }
}